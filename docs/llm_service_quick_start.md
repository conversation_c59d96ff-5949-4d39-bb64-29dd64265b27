# LLM服务层快速开始指南

## 概述

本指南将帮助您快速上手统一LLM服务层，从基础配置到高级功能的使用。

## 安装和配置

### 1. 环境要求

- Python 3.8+
- 已安装金融分析项目的依赖包

### 2. 基础配置

在项目根目录的 `.env` 文件中添加以下配置：

```bash
# 基础LLM服务配置
DEFAULT_LLM_PROVIDER=windmill
LLM_FALLBACK_ENABLED=true
LLM_MAX_RETRIES=3

# Windmill配置（保持现有配置）
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output
WINDMILL_ENABLED=true
```

### 3. 验证安装

运行以下代码验证LLM服务是否正常工作：

```python
import asyncio
from financial_analysis.llm_compatibility import test_llm_service

async def verify_installation():
    test_results = await test_llm_service()
    print("LLM服务测试结果:", test_results)

# 运行验证
asyncio.run(verify_installation())
```

## 基础使用

### 1. 简单文本生成

最简单的使用方式，与现有代码完全兼容：

```python
import asyncio
from financial_analysis import unified_llm_service

async def basic_example():
    # 生成投资分析
    result = await unified_llm_service.generate_text_analysis(
        prompt="请分析苹果公司(AAPL)的投资价值",
        system_instruction="你是一位专业的金融分析师"
    )
    
    print("分析结果:", result)

# 运行示例
asyncio.run(basic_example())
```

### 2. 启用搜索功能

对于需要最新信息的分析，可以启用搜索功能：

```python
async def search_enabled_example():
    result = await unified_llm_service.generate_text_analysis(
        prompt="分析当前科技股的市场趋势",
        system_instruction="你是金融市场专家，请基于最新信息进行分析",
        search=True  # 启用搜索获取最新信息
    )
    
    print("基于最新信息的分析:", result)

asyncio.run(search_enabled_example())
```

### 3. 获取详细响应信息

如果需要了解更多响应细节（如token使用量、响应时间等）：

```python
async def detailed_response_example():
    response = await unified_llm_service.generate_text_with_metadata(
        prompt="评估特斯拉股票的风险等级",
        system_instruction="你是风险评估专家",
        temperature=0.7,  # 设置创造性参数
        max_tokens=500    # 限制响应长度
    )
    
    if response:
        print(f"分析内容: {response.content}")
        print(f"使用的提供商: {response.provider}")
        print(f"使用的模型: {response.model}")
        print(f"Token使用量: {response.usage.total_tokens}")
        print(f"响应时间: {response.response_time:.2f}秒")

asyncio.run(detailed_response_example())
```

## 兼容性使用

### 现有代码无缝迁移

如果您的项目中已经使用了Windmill客户端，无需修改任何代码：

```python
# 原有代码保持不变
from financial_analysis.windmill_client import windmill_client

async def existing_code_example():
    # 这段代码无需任何修改，底层自动使用新的LLM服务层
    result = await windmill_client.generate_text_analysis(
        prompt="分析股票市场",
        system_instruction="你是金融专家",
        search=True
    )
    
    return result
```

### 使用增强客户端

推荐使用增强的Windmill客户端，获得更好的性能和功能：

```python
from financial_analysis.llm_compatibility import enhanced_windmill_client

async def enhanced_client_example():
    # 完全兼容原有接口，但底层使用统一LLM服务
    result = await enhanced_windmill_client.generate_text_analysis(
        prompt="分析比特币价格趋势",
        system_instruction="你是加密货币分析专家",
        search=True
    )
    
    # 批量分析功能也完全兼容
    news_items = [
        {"title": "苹果发布新产品", "content": "苹果公司今日发布..."},
        {"title": "科技股普涨", "content": "今日科技股表现强劲..."}
    ]
    stock_info = {"symbol": "AAPL", "name": "苹果公司", "sector": "科技"}
    
    relevance_results = await enhanced_windmill_client.batch_analyze_stock_relevance(
        news_items, stock_info
    )
    
    return result, relevance_results

asyncio.run(enhanced_client_example())
```

## 高级功能

### 1. 指定特定提供商

当您需要使用特定的LLM提供商时：

```python
async def specific_provider_example():
    # 明确指定使用Windmill提供商
    result = await unified_llm_service.generate_text_analysis(
        prompt="分析黄金投资机会",
        provider_id="windmill"  # 指定提供商
    )
    
    print("使用Windmill的分析:", result)

asyncio.run(specific_provider_example())
```

### 2. 健康检查和监控

监控LLM服务的健康状态：

```python
async def monitoring_example():
    # 检查所有提供商的健康状态
    health_status = await unified_llm_service.health_check()
    print("健康状态:", health_status)
    
    # 获取服务统计信息
    stats = unified_llm_service.get_stats()
    print("服务统计:", stats)
    
    # 获取可用提供商列表
    providers = unified_llm_service.get_available_providers()
    print("可用提供商:", providers)

asyncio.run(monitoring_example())
```

### 3. 错误处理

实现完善的错误处理机制：

```python
from financial_analysis.llm_service import LLMServiceError, RateLimitError, ValidationError

async def error_handling_example():
    try:
        result = await unified_llm_service.generate_text_analysis(
            prompt="分析股票投资策略"
        )
        print("分析成功:", result)
        
    except RateLimitError as e:
        print(f"速率限制错误: {e.message}")
        print(f"建议等待 {e.retry_after} 秒后重试")
        
    except ValidationError as e:
        print(f"参数验证错误: {e.message}")
        
    except LLMServiceError as e:
        print(f"LLM服务错误: {e.message}")
        if e.is_retryable:
            print("该错误可以重试")
        
    except Exception as e:
        print(f"未知错误: {str(e)}")

asyncio.run(error_handling_example())
```

## 实际应用场景

### 1. 股票分析报告生成

```python
async def stock_analysis_report():
    stock_symbol = "TSLA"
    stock_name = "特斯拉"
    
    # 生成综合分析报告
    prompt = f"""
    请为 {stock_name} ({stock_symbol}) 生成一份综合投资分析报告，包括：
    
    1. 公司基本面分析
    2. 技术面分析
    3. 行业地位评估
    4. 风险因素分析
    5. 投资建议和目标价位
    
    请基于最新的市场信息进行分析。
    """
    
    report = await unified_llm_service.generate_text_analysis(
        prompt=prompt,
        system_instruction="你是一位资深的股票分析师，具有10年以上的投资分析经验",
        search=True,  # 获取最新市场信息
        temperature=0.3,  # 较低的随机性，确保分析的客观性
        max_tokens=1500
    )
    
    print(f"{stock_name} 投资分析报告:")
    print("=" * 50)
    print(report)

asyncio.run(stock_analysis_report())
```

### 2. 新闻情感分析

```python
async def news_sentiment_analysis():
    news_title = "美联储宣布加息25个基点"
    news_content = "美联储在今日的货币政策会议上决定将联邦基金利率上调25个基点..."
    
    prompt = f"""
    请分析以下新闻对股票市场的影响：
    
    标题: {news_title}
    内容: {news_content}
    
    请从以下角度进行分析：
    1. 新闻的整体情感倾向（积极/消极/中性）
    2. 对不同行业的影响程度
    3. 对整体市场的短期和长期影响
    4. 投资者应该关注的要点
    """
    
    analysis = await unified_llm_service.generate_text_analysis(
        prompt=prompt,
        system_instruction="你是专业的金融新闻分析师，擅长解读宏观经济政策对市场的影响"
    )
    
    print("新闻影响分析:")
    print("=" * 40)
    print(analysis)

asyncio.run(news_sentiment_analysis())
```

### 3. 投资组合建议

```python
async def portfolio_recommendation():
    user_profile = {
        "age": 35,
        "risk_tolerance": "中等",
        "investment_horizon": "5-10年",
        "investment_amount": "50万元",
        "investment_goal": "退休储备"
    }
    
    prompt = f"""
    请为以下投资者制定个性化的投资组合建议：
    
    投资者画像：
    - 年龄：{user_profile['age']}岁
    - 风险承受能力：{user_profile['risk_tolerance']}
    - 投资期限：{user_profile['investment_horizon']}
    - 投资金额：{user_profile['investment_amount']}
    - 投资目标：{user_profile['investment_goal']}
    
    请提供：
    1. 资产配置建议（股票、债券、现金等比例）
    2. 具体的投资标的推荐
    3. 风险控制措施
    4. 定期调整策略
    """
    
    recommendation = await unified_llm_service.generate_text_analysis(
        prompt=prompt,
        system_instruction="你是专业的财富管理顾问，具有丰富的资产配置经验",
        search=True,  # 获取最新的市场信息
        temperature=0.4
    )
    
    print("个性化投资组合建议:")
    print("=" * 50)
    print(recommendation)

asyncio.run(portfolio_recommendation())
```

## 性能优化建议

### 1. 合理设置参数

```python
# 对于需要精确分析的场景
precise_analysis = await unified_llm_service.generate_text_analysis(
    prompt="计算股票的内在价值",
    temperature=0.1,  # 低随机性，确保计算准确
    max_tokens=800
)

# 对于需要创意性分析的场景
creative_analysis = await unified_llm_service.generate_text_analysis(
    prompt="预测未来科技趋势对投资的影响",
    temperature=0.8,  # 高随机性，鼓励创新思考
    max_tokens=1200
)
```

### 2. 批量处理

```python
async def batch_processing_example():
    stocks = ["AAPL", "GOOGL", "MSFT", "TSLA"]
    
    # 并发处理多个股票分析
    tasks = []
    for stock in stocks:
        task = unified_llm_service.generate_text_analysis(
            prompt=f"简要分析 {stock} 的投资价值",
            system_instruction="提供简洁的投资建议"
        )
        tasks.append(task)
    
    # 等待所有分析完成
    results = await asyncio.gather(*tasks)
    
    for stock, result in zip(stocks, results):
        print(f"{stock}: {result}")

asyncio.run(batch_processing_example())
```

## 故障排除

### 常见问题及解决方案

1. **服务初始化失败**
```python
from financial_analysis.llm_compatibility import get_llm_service_status

# 检查服务状态
status = get_llm_service_status()
if not status["service_available"]:
    print("服务不可用，错误:", status.get("error"))
```

2. **请求超时**
```python
# 增加超时时间
from financial_analysis import settings
settings.llm_timeout = 180  # 设置为3分钟
```

3. **响应质量不佳**
```python
# 优化提示词和参数
result = await unified_llm_service.generate_text_analysis(
    prompt="请详细分析...",  # 提供更具体的指令
    system_instruction="你是专业的...",  # 明确角色定位
    temperature=0.3,  # 降低随机性
    max_tokens=1000  # 给予足够的响应空间
)
```

## 下一步

- 查看 [LLM服务层架构文档](llm_service_architecture.md) 了解详细的架构设计
- 参考 [API参考文档](llm_service_api_reference.md) 获取完整的API说明
- 运行项目中的示例代码进行实践
- 根据具体需求配置多个LLM提供商

## 技术支持

如果在使用过程中遇到问题，请：

1. 检查配置是否正确
2. 查看日志文件获取详细错误信息
3. 运行健康检查确认服务状态
4. 参考故障排除部分的解决方案

通过统一LLM服务层，您可以轻松地为金融分析项目添加强大的AI能力，同时保持代码的简洁性和可维护性。
