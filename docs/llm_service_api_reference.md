# LLM服务层API参考文档

## 概述

本文档提供了统一LLM服务层的详细API参考，包括所有类、方法、参数和返回值的说明。

## 核心API

### UnifiedLLMService

统一LLM服务的主要入口类。

#### 方法

##### `generate_text_analysis(prompt, system_instruction=None, search=False, provider_id=None, **kwargs)`

生成文本分析（兼容现有接口）。

**参数:**
- `prompt` (str): 分析提示词，必填
- `system_instruction` (str, optional): 系统指令
- `search` (bool, optional): 是否启用搜索工具，默认False
- `provider_id` (str, optional): 指定的提供商ID，默认使用配置的默认提供商
- `**kwargs`: 其他LLM参数（temperature、max_tokens等）

**返回值:**
- `Optional[str]`: 生成的分析文本，失败时返回None

**示例:**
```python
result = await unified_llm_service.generate_text_analysis(
    prompt="请分析苹果公司的投资价值",
    system_instruction="你是专业的金融分析师",
    search=True,
    temperature=0.7
)
```

##### `generate_text_with_metadata(prompt, system_instruction=None, search=False, provider_id=None, **kwargs)`

生成文本并返回完整的响应元数据。

**参数:**
- 同 `generate_text_analysis`

**返回值:**
- `Optional[LLMResponse]`: 完整的LLM响应对象，包含内容、元数据和使用统计

**示例:**
```python
response = await unified_llm_service.generate_text_with_metadata(
    prompt="分析市场趋势",
    provider_id="windmill"
)

print(f"内容: {response.content}")
print(f"提供商: {response.provider}")
print(f"Token使用: {response.usage.total_tokens}")
```

##### `generate_text_analysis_stream(prompt, system_instruction=None, search=False, provider_id=None, enable_cancellation=True, **kwargs)`

生成文本分析（流式响应）。

**参数:**
- `prompt` (str): 分析提示词，必填
- `system_instruction` (str, optional): 系统指令
- `search` (bool, optional): 是否启用搜索工具，默认False
- `provider_id` (str, optional): 指定的提供商ID，默认使用配置的默认提供商
- `enable_cancellation` (bool, optional): 是否启用取消功能，默认True
- `**kwargs`: 其他LLM参数（temperature、max_tokens等）

**返回值:**
- `AsyncGenerator[LLMStreamChunk, None]`: 流式响应块生成器

**示例:**
```python
async for chunk in unified_llm_service.generate_text_analysis_stream(
    prompt="请分析苹果公司的投资价值",
    system_instruction="你是专业的金融分析师",
    search=True,
    temperature=0.7
):
    print(f"块 {chunk.chunk_id}: {chunk.delta}")
    if chunk.progress_percentage:
        print(f"进度: {chunk.progress_percentage:.1f}%")

    if chunk.is_final:
        print(f"完成！最终内容: {chunk.content}")
        break
```

##### `cancel_stream(token_id, reason="用户取消")`

取消流式响应。

**参数:**
- `token_id` (str): 取消令牌ID
- `reason` (str, optional): 取消原因，默认"用户取消"

**返回值:**
- `bool`: 是否成功取消

**示例:**
```python
# 在流式响应过程中取消
success = unified_llm_service.cancel_stream(token_id, "用户主动取消")
```

##### `get_active_streams()`

获取活跃的流式响应列表。

**返回值:**
- `List[str]`: 活跃流式响应的令牌ID列表

##### `get_stream_status(token_id)`

获取流式响应状态。

**参数:**
- `token_id` (str): 取消令牌ID

**返回值:**
- `Optional[Dict[str, Any]]`: 流式响应状态信息

##### `health_check(provider_id=None)`

执行健康检查。

**参数:**
- `provider_id` (str, optional): 指定的提供商ID，为None时检查所有提供商

**返回值:**
- `Dict[str, bool]`: 健康状态字典，键为提供商ID，值为健康状态

**示例:**
```python
# 检查所有提供商
health = await unified_llm_service.health_check()
# 结果: {"windmill": True, "openai": False}

# 检查特定提供商
health = await unified_llm_service.health_check("windmill")
# 结果: {"windmill": True}
```

##### `get_stats()`

获取服务统计信息。

**返回值:**
- `Dict[str, Any]`: 统计信息字典

**示例:**
```python
stats = unified_llm_service.get_stats()
# 结果包含: default_provider, total_providers, enabled_providers, provider_stats
```

##### `get_available_providers()`

获取可用的提供商列表。

**返回值:**
- `List[str]`: 提供商ID列表

## 数据模型

### LLMRequest

LLM请求模型。

**字段:**
- `prompt` (str): 提示词，必填
- `system_instruction` (Optional[str]): 系统指令
- `search` (bool): 是否启用搜索工具，默认False
- `stream` (bool): 是否启用流式响应，默认False
- `temperature` (Optional[float]): 温度参数，控制随机性 (0.0-2.0)
- `max_tokens` (Optional[int]): 最大生成token数
- `top_p` (Optional[float]): 核采样参数 (0.0-1.0)
- `top_k` (Optional[int]): Top-K采样参数
- `frequency_penalty` (Optional[float]): 频率惩罚 (-2.0-2.0)
- `presence_penalty` (Optional[float]): 存在惩罚 (-2.0-2.0)
- `extra_params` (Dict[str, Any]): 提供商特定的额外参数

### LLMResponse

LLM响应模型。

**字段:**
- `content` (str): 生成的文本内容
- `model` (str): 使用的模型名称
- `provider` (str): LLM提供商
- `request_id` (Optional[str]): 请求ID
- `usage` (Optional[LLMUsage]): 使用统计
- `created_at` (datetime): 响应创建时间
- `response_time` (Optional[float]): 响应时间（秒）
- `finish_reason` (Optional[str]): 完成原因
- `confidence_score` (Optional[float]): 置信度评分 (0.0-1.0)
- `extra_data` (Dict[str, Any]): 提供商特定的额外数据

### LLMStreamChunk

LLM流式响应块模型。

**字段:**
- `content` (str): 当前块的文本内容（累积内容）
- `delta` (str): 相对于上一块的增量内容
- `chunk_id` (int): 块序号
- `is_final` (bool): 是否为最后一块，默认False
- `progress_percentage` (Optional[float]): 进度百分比 (0.0-100.0)
- `estimated_total_chunks` (Optional[int]): 预估总块数
- `is_cancelled` (bool): 是否已取消，默认False
- `cancellation_token` (Optional[str]): 取消令牌
- `provider` (Optional[str]): LLM提供商
- `model` (Optional[str]): 使用的模型
- `is_simulated_stream` (bool): 是否为模拟流式响应，默认False
- `timestamp` (datetime): 块生成时间
- `chunk_duration` (Optional[float]): 块生成耗时（秒）
- `confidence_score` (Optional[float]): 当前块的置信度 (0.0-1.0)
- `has_error` (bool): 是否包含错误，默认False
- `error_message` (Optional[str]): 错误消息

### LLMUsage

LLM使用统计模型。

**字段:**
- `prompt_tokens` (Optional[int]): 输入token数
- `completion_tokens` (Optional[int]): 输出token数
- `total_tokens` (Optional[int]): 总token数
- `prompt_cost` (Optional[float]): 输入成本
- `completion_cost` (Optional[float]): 输出成本
- `total_cost` (Optional[float]): 总成本

### LLMStreamConfig

LLM流式响应配置模型。

**字段:**
- `enabled` (bool): 是否启用流式响应，默认True
- `chunk_size` (int): 流式块大小（字符数），默认50
- `chunk_delay` (float): 块之间的延迟（秒），默认0.1
- `split_by_sentence` (bool): 是否按句子分割，默认True
- `split_by_word` (bool): 是否按单词分割，默认False
- `preserve_formatting` (bool): 是否保持格式，默认True
- `max_chunks` (Optional[int]): 最大块数限制
- `timeout` (int): 流式响应超时时间（秒），默认300
- `buffer_size` (int): 缓冲区大小，默认1024
- `enable_progress` (bool): 是否启用进度报告，默认True
- `enable_cancellation` (bool): 是否支持取消，默认True

### StreamCancellationToken

流式响应取消令牌模型。

**字段:**
- `token_id` (str): 取消令牌唯一标识
- `created_at` (datetime): 创建时间
- `is_cancelled` (bool): 是否已取消，默认False
- `cancelled_at` (Optional[datetime]): 取消时间
- `reason` (Optional[str]): 取消原因

**方法:**
- `cancel(reason="用户取消")`: 取消流式响应

### StreamProgress

流式响应进度模型。

**字段:**
- `current_chunk` (int): 当前块序号
- `total_chunks` (Optional[int]): 总块数
- `percentage` (Optional[float]): 进度百分比
- `current_length` (int): 当前内容长度，默认0
- `estimated_total_length` (Optional[int]): 预估总长度
- `start_time` (datetime): 开始时间
- `current_time` (datetime): 当前时间
- `estimated_completion_time` (Optional[datetime]): 预估完成时间
- `chunks_per_second` (Optional[float]): 每秒块数
- `characters_per_second` (Optional[float]): 每秒字符数

**方法:**
- `update_progress(chunk_id, content_length, total_chunks=None)`: 更新进度信息

### LLMProviderConfig

LLM提供商配置模型。

**字段:**
- `provider_id` (str): 提供商唯一标识
- `provider_name` (str): 提供商名称
- `provider_type` (str): 提供商类型
- `base_url` (Optional[str]): API基础URL
- `api_key` (Optional[str]): API密钥
- `default_model` (str): 默认模型名称
- `available_models` (List[str]): 可用模型列表
- `supports_streaming` (bool): 是否支持原生流式响应，默认False
- `stream_config` (LLMStreamConfig): 流式响应配置
- `max_tokens_limit` (Optional[int]): 最大token限制
- `rate_limit_rpm` (Optional[int]): 每分钟请求限制
- `rate_limit_tpm` (Optional[int]): 每分钟token限制
- `max_retries` (int): 最大重试次数，默认3
- `retry_delay` (float): 重试延迟（秒），默认1.0
- `timeout` (int): 请求超时时间（秒），默认60
- `enabled` (bool): 是否启用，默认True
- `priority` (int): 优先级，数字越小优先级越高，默认1
- `extra_config` (Dict[str, Any]): 提供商特定的额外配置

## 提供商管理

### LLMProviderFactory

LLM提供商工厂类。

#### 类方法

##### `create_provider(config)`

创建LLM提供商实例。

**参数:**
- `config` (LLMProviderConfig): 提供商配置

**返回值:**
- `BaseLLMProvider`: LLM提供商实例

##### `create_windmill_provider(provider_id="windmill", provider_name="Windmill", ...)`

创建Windmill提供商的便捷方法。

**参数:**
- `provider_id` (str): 提供商ID，默认"windmill"
- `provider_name` (str): 提供商名称，默认"Windmill"
- `base_url` (str): Windmill基础URL
- `api_key` (str): API密钥
- `workspace` (str): 工作空间
- `folder` (str): 文件夹
- `script` (str): 脚本名称
- `enabled` (bool): 是否启用，默认True
- `priority` (int): 优先级，默认1

**返回值:**
- `WindmillLLMProvider`: Windmill LLM提供商实例

##### `create_mock_provider(provider_id="mock", provider_name="Mock LLM", ...)`

创建模拟提供商的便捷方法。

**参数:**
- `provider_id` (str): 提供商ID，默认"mock"
- `provider_name` (str): 提供商名称，默认"Mock LLM"
- `enabled` (bool): 是否启用，默认True
- `priority` (int): 优先级，默认999

**返回值:**
- `MockLLMProvider`: 模拟LLM提供商实例

##### `get_supported_types()`

获取支持的提供商类型列表。

**返回值:**
- `List[str]`: 提供商类型列表

### LLMServiceRegistry

LLM服务注册表。

#### 方法

##### `register_provider(provider)`

注册LLM提供商。

**参数:**
- `provider` (BaseLLMProvider): LLM提供商实例

##### `unregister_provider(provider_id)`

注销LLM提供商。

**参数:**
- `provider_id` (str): 提供商ID

##### `get_provider(provider_id)`

获取LLM提供商。

**参数:**
- `provider_id` (str): 提供商ID

**返回值:**
- `Optional[BaseLLMProvider]`: LLM提供商实例，如果不存在返回None

##### `get_enabled_providers()`

获取所有启用的LLM提供商。

**返回值:**
- `List[BaseLLMProvider]`: 启用的提供商列表，按优先级排序

##### `get_all_providers()`

获取所有LLM提供商。

**返回值:**
- `Dict[str, BaseLLMProvider]`: 提供商字典

## 兼容性API

### EnhancedWindmillClient

增强的Windmill客户端，保持与原有接口的完全兼容性。

#### 方法

##### `generate_text_analysis(prompt, system_instruction=None, search=False)`

使用统一LLM服务生成文本分析（保持接口兼容性）。

**参数:**
- `prompt` (str): 分析提示词
- `system_instruction` (str, optional): 系统指令
- `search` (bool, optional): 是否需要进行搜索

**返回值:**
- `Optional[str]`: 生成的分析文本，如果失败返回None

##### `batch_analyze_stock_relevance(news_items, stock_info)`

批量分析新闻与股票的相关性（保持接口兼容性）。

**参数:**
- `news_items` (list): 新闻条目列表
- `stock_info` (dict): 股票信息

**返回值:**
- `Optional[list]`: 分析结果列表

##### `generate_text_analysis_stream(prompt, system_instruction=None, search=False)`

使用统一LLM服务生成文本分析（流式响应，兼容性接口）。

**参数:**
- `prompt` (str): 分析提示词
- `system_instruction` (str, optional): 系统指令
- `search` (bool, optional): 是否需要进行搜索

**返回值:**
- `AsyncGenerator[LLMStreamChunk, None]`: 流式响应块生成器

**示例:**
```python
async for chunk in enhanced_windmill_client.generate_text_analysis_stream(
    prompt="分析股票趋势",
    search=True
):
    print(f"接收到块: {chunk.delta}")
    if chunk.is_final:
        print(f"分析完成: {chunk.content}")
        break
```

## 异常处理

### 异常类型

#### `LLMServiceError`

LLM服务异常基类。

**属性:**
- `message` (str): 错误消息
- `error_code` (str): 错误代码
- `provider` (str): 提供商
- `is_retryable` (bool): 是否可重试

#### `RateLimitError`

速率限制异常。

**属性:**
- 继承自 `LLMServiceError`
- `retry_after` (int): 建议重试间隔（秒）

#### `ModelNotFoundError`

模型未找到异常。

**属性:**
- 继承自 `LLMServiceError`
- `model` (str): 模型名称

#### `ValidationError`

参数验证异常。

**属性:**
- 继承自 `LLMServiceError`

## 工具函数

### `get_llm_service_status()`

获取LLM服务状态信息。

**返回值:**
- `Dict[str, Any]`: 服务状态字典

### `test_llm_service()`

测试LLM服务功能。

**返回值:**
- `Dict[str, Any]`: 测试结果字典

## 全局实例

### `unified_llm_service`

全局统一LLM服务实例。

### `llm_registry`

全局LLM服务注册表实例。

### `llm_manager`

全局LLM服务管理器实例。

### `enhanced_windmill_client`

全局增强Windmill客户端实例。

## 使用示例

### 基础使用

```python
from financial_analysis import unified_llm_service

# 简单文本生成
result = await unified_llm_service.generate_text_analysis(
    prompt="分析苹果公司股票",
    system_instruction="你是金融专家"
)

# 带完整元数据的生成
response = await unified_llm_service.generate_text_with_metadata(
    prompt="分析市场趋势",
    temperature=0.7,
    max_tokens=1000
)
```

### 提供商管理

```python
from financial_analysis import LLMProviderFactory, llm_registry

# 创建自定义提供商
provider = LLMProviderFactory.create_windmill_provider(
    provider_id="custom",
    base_url="https://custom.api.com",
    api_key="custom_key"
)

# 注册提供商
llm_registry.register_provider(provider)
```

### 流式响应使用

```python
from financial_analysis import unified_llm_service

# 基础流式响应
async for chunk in unified_llm_service.generate_text_analysis_stream(
    prompt="分析苹果公司投资价值",
    system_instruction="你是金融专家"
):
    print(f"块 {chunk.chunk_id}: {chunk.delta}")

    # 显示进度
    if chunk.progress_percentage:
        print(f"进度: {chunk.progress_percentage:.1f}%")

    # 检查是否完成
    if chunk.is_final:
        print(f"完成！最终内容: {chunk.content}")
        break

# 带取消控制的流式响应
chunks_received = 0
async for chunk in unified_llm_service.generate_text_analysis_stream(
    prompt="详细分析市场趋势",
    enable_cancellation=True
):
    chunks_received += 1
    print(f"接收块 {chunks_received}: {len(chunk.delta)} 字符")

    # 在第5个块后取消
    if chunks_received == 5 and chunk.cancellation_token:
        unified_llm_service.cancel_stream(chunk.cancellation_token, "演示取消")

    if chunk.is_cancelled:
        print("流式响应已取消")
        break

    if chunk.is_final:
        print("流式响应完成")
        break

# 监控流式响应状态
active_streams = unified_llm_service.get_active_streams()
print(f"当前活跃流式响应: {len(active_streams)} 个")

for token_id in active_streams:
    status = unified_llm_service.get_stream_status(token_id)
    print(f"流式响应 {token_id}: {status}")
```

### 兼容性使用

```python
from financial_analysis.llm_compatibility import enhanced_windmill_client

# 使用增强客户端（完全兼容原接口）
result = await enhanced_windmill_client.generate_text_analysis(
    prompt="分析股票",
    search=True
)

# 使用增强客户端的流式接口
async for chunk in enhanced_windmill_client.generate_text_analysis_stream(
    prompt="分析股票趋势",
    search=True
):
    print(f"流式块: {chunk.delta}")
    if chunk.is_final:
        break
```
