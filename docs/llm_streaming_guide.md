# LLM流式响应功能指南

## 概述

LLM流式响应功能为金融分析系统提供了实时的文本生成体验，用户可以在LLM生成内容的同时看到逐步输出的结果，而不需要等待完整响应完成。

## 核心特性

### 🚀 智能降级机制
- **原生流式支持**: 对于支持流式响应的LLM提供商，直接使用原生流式API
- **智能模拟流式**: 对于不支持流式的提供商（如Windmill），自动降级为模拟流式输出
- **无缝切换**: 用户无需关心底层实现，统一的API接口

### 📊 进度跟踪
- **实时进度**: 显示当前生成进度百分比
- **块计数**: 显示当前块数和预估总块数
- **时间统计**: 记录生成耗时和平均速度

### 🛑 取消控制
- **中途取消**: 支持在生成过程中取消流式响应
- **令牌管理**: 自动管理取消令牌的生命周期
- **状态跟踪**: 实时监控流式响应状态

### 🔧 灵活配置
- **分割策略**: 支持按句子、单词、字符等多种分割方式
- **块大小控制**: 可配置每个流式块的大小
- **延迟控制**: 可调整块之间的输出延迟

## 快速开始

### 基础流式响应

```python
from financial_analysis import unified_llm_service

# 基础流式文本生成
async for chunk in unified_llm_service.generate_text_analysis_stream(
    prompt="分析苹果公司的投资价值",
    system_instruction="你是专业的金融分析师"
):
    print(f"接收到: {chunk.delta}")
    
    # 显示进度
    if chunk.progress_percentage:
        print(f"进度: {chunk.progress_percentage:.1f}%")
    
    # 检查是否完成
    if chunk.is_final:
        print(f"完成！最终内容: {chunk.content}")
        break
```

### 带取消控制的流式响应

```python
# 带取消控制的流式响应
chunks_received = 0
async for chunk in unified_llm_service.generate_text_analysis_stream(
    prompt="详细分析市场趋势",
    enable_cancellation=True
):
    chunks_received += 1
    print(f"接收块 {chunks_received}: {len(chunk.delta)} 字符")
    
    # 在第5个块后取消
    if chunks_received == 5 and chunk.cancellation_token:
        unified_llm_service.cancel_stream(chunk.cancellation_token, "演示取消")
    
    if chunk.is_cancelled:
        print("流式响应已取消")
        break
    
    if chunk.is_final:
        print("流式响应完成")
        break
```

### 兼容性接口

```python
from financial_analysis.llm_compatibility import enhanced_windmill_client

# 使用增强Windmill客户端的流式接口
async for chunk in enhanced_windmill_client.generate_text_analysis_stream(
    prompt="分析股票趋势",
    search=True
):
    print(f"流式块: {chunk.delta}")
    if chunk.is_final:
        break
```

## 数据模型

### LLMStreamChunk

流式响应块模型，包含以下关键字段：

```python
class LLMStreamChunk:
    content: str                    # 累积内容
    delta: str                      # 增量内容
    chunk_id: int                   # 块序号
    is_final: bool                  # 是否为最后一块
    progress_percentage: float      # 进度百分比
    is_cancelled: bool              # 是否已取消
    cancellation_token: str         # 取消令牌
    provider: str                   # LLM提供商
    is_simulated_stream: bool       # 是否为模拟流式
    has_error: bool                 # 是否包含错误
    error_message: str              # 错误消息
```

### StreamCancellationToken

取消令牌模型：

```python
class StreamCancellationToken:
    token_id: str                   # 令牌唯一标识
    is_cancelled: bool              # 是否已取消
    cancelled_at: datetime          # 取消时间
    reason: str                     # 取消原因
    
    def cancel(self, reason: str):  # 取消方法
        pass
```

## 配置选项

### LLMStreamConfig

流式响应配置：

```python
class LLMStreamConfig:
    enabled: bool = True            # 是否启用流式响应
    chunk_size: int = 50           # 块大小（字符数）
    chunk_delay: float = 0.1       # 块间延迟（秒）
    split_by_sentence: bool = True  # 按句子分割
    enable_progress: bool = True    # 启用进度报告
    enable_cancellation: bool = True # 支持取消
    timeout: int = 300             # 超时时间（秒）
    buffer_size: int = 1024        # 缓冲区大小
```

## 管理接口

### 流式响应管理

```python
# 获取活跃的流式响应
active_streams = unified_llm_service.get_active_streams()
print(f"当前活跃流式响应: {len(active_streams)} 个")

# 取消特定流式响应
success = unified_llm_service.cancel_stream(token_id, "用户取消")

# 获取流式响应状态
status = unified_llm_service.get_stream_status(token_id)
if status:
    print(f"状态: {status}")
```

## 最佳实践

### 1. 错误处理

```python
try:
    async for chunk in unified_llm_service.generate_text_analysis_stream(prompt):
        if chunk.has_error:
            print(f"错误: {chunk.error_message}")
            break
        
        # 处理正常块
        process_chunk(chunk)
        
except Exception as e:
    print(f"流式响应异常: {str(e)}")
```

### 2. 进度显示

```python
async for chunk in unified_llm_service.generate_text_analysis_stream(prompt):
    # 显示进度条
    if chunk.progress_percentage:
        progress_bar = "█" * int(chunk.progress_percentage / 5)
        print(f"\r进度: [{progress_bar:<20}] {chunk.progress_percentage:.1f}%", end="")
    
    if chunk.is_final:
        print("\n完成！")
        break
```

### 3. 内容累积

```python
accumulated_content = ""
async for chunk in unified_llm_service.generate_text_analysis_stream(prompt):
    # 使用累积内容而不是手动拼接
    accumulated_content = chunk.content
    
    # 实时更新UI
    update_ui(accumulated_content)
    
    if chunk.is_final:
        save_final_content(accumulated_content)
        break
```

## 性能优化

### 1. 合理设置块大小
- **小块**: 更流畅的用户体验，但网络开销较大
- **大块**: 减少网络开销，但用户体验不够流畅
- **推荐**: 30-100字符，根据内容类型调整

### 2. 缓冲区管理
- 启用缓冲区可以平滑网络波动
- 根据网络条件调整缓冲区大小

### 3. 取消机制
- 及时清理不需要的流式响应
- 避免长时间运行的无用流式响应

## 故障排除

### 常见问题

1. **流式响应卡住**
   - 检查网络连接
   - 验证取消令牌状态
   - 查看超时设置

2. **进度显示不准确**
   - 文本分割策略可能需要调整
   - 检查内容长度估算

3. **取消功能不工作**
   - 确认取消令牌正确传递
   - 检查流式响应状态

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('financial_analysis.llm_streaming').setLevel(logging.DEBUG)

# 监控流式响应状态
async for chunk in stream:
    print(f"块 {chunk.chunk_id}: {len(chunk.delta)} 字符, "
          f"累积: {len(chunk.content)} 字符, "
          f"进度: {chunk.progress_percentage}%")
```

## 总结

LLM流式响应功能为金融分析系统提供了更好的用户体验，通过智能降级机制确保了在不同LLM提供商之间的一致性。合理使用流式响应功能可以显著提升应用的交互性和用户满意度。
