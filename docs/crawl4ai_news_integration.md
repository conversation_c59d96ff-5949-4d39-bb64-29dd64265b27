# Crawl4AI 股票新闻爬取和分析系统

## 概述

本系统集成了 Crawl4AI HTTP 爬取接口和 Windmill text_generation 接口，实现了智能的股票新闻爬取、验证和价值分析功能。系统能够从多个新闻源获取特定股票的最新新闻，通过 AI 验证新闻的新鲜度，并分析其对投资决策的价值。

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                 股票新闻爬取分析系统                         │
├─────────────────────────────────────────────────────────────┤
│                StockNewsCrawlAnalyzer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   新闻爬取器    │  │   新闻验证器    │  │  报告生成器 │  │
│  │ StockNewsCrawler│  │ NewsValidator   │  │             │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
│           │                     │                   │       │
│           ▼                     ▼                   ▼       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Crawl4AI      │  │   Windmill      │  │  价值评估   │  │
│  │   HTTP API      │  │ text_generation │  │   算法      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. Crawl4aiNewsClient
- **功能**: Crawl4AI HTTP 接口客户端
- **特性**: 
  - 异步HTTP请求
  - 健康检查
  - 错误处理和重试
  - 支持多种提取策略

### 2. StockNewsCrawler
- **功能**: 股票新闻爬取器
- **特性**:
  - 多源新闻聚合
  - 智能去重算法
  - 时间解析和标准化
  - 可配置的新闻源

### 3. NewsValidator
- **功能**: 新闻验证和价值分析器
- **特性**:
  - AI驱动的新鲜度验证
  - 投资价值评估
  - 情感分析
  - 影响程度评级

### 4. StockNewsCrawlAnalyzer
- **功能**: 统一的新闻分析接口
- **特性**:
  - 端到端的分析流程
  - 报告生成
  - 高价值新闻筛选
  - 批量处理优化

## 新闻源配置

### 当前支持的新闻源

1. **新浪财经**
   - 搜索URL: `https://search.sina.com.cn/?q={stock_name}&range=all&c=news&sort=time`
   - 提取策略: CSS选择器
   - 字段: 标题、链接、摘要、时间

2. **东方财富**
   - 搜索URL: `https://so.eastmoney.com/news/s?keyword={stock_name}`
   - 提取策略: CSS选择器
   - 字段: 标题、链接、摘要、时间

3. **中国证券网**
   - 搜索URL: `https://search.cnstock.com/search/news?searchword={stock_name}`
   - 提取策略: CSS选择器
   - 字段: 标题、链接、摘要、时间

### 扩展新闻源

```python
# 在 StockNewsCrawler._get_news_sources() 中添加新源
"new_source": {
    "name": "新闻源名称",
    "search_url_template": "https://example.com/search?q={stock_name}",
    "extraction_config": {
        "type": "JsonCssExtractionStrategy",
        "params": {
            "schema": {
                "type": "dict",
                "value": {
                    "baseSelector": ".news-item",
                    "fields": [
                        {"name": "title", "selector": ".title", "type": "text"},
                        {"name": "url", "selector": ".title a", "type": "attribute", "attribute": "href"},
                        {"name": "summary", "selector": ".summary", "type": "text"},
                        {"name": "time", "selector": ".time", "type": "text"}
                    ]
                }
            }
        }
    }
}
```

## 新闻验证和价值分析

### 验证标准

1. **新鲜度评估**
   - 时间因素: 24小时内的新闻优先
   - 重要性: 重大事件可忽略时间限制
   - 评分范围: 0-100分

2. **价值分析**
   - 财报公告: 90-100分
   - 重大公告: 80-90分
   - 政策影响: 70-80分
   - 行业动态: 60-70分
   - 一般新闻: 50-60分

3. **影响程度**
   - High: 直接影响股价的重大事件
   - Medium: 间接影响的重要信息
   - Low: 一般性新闻和背景信息

4. **情感分析**
   - Positive: 利好消息
   - Negative: 利空消息
   - Neutral: 中性信息

## 使用方法

### 基础用法

```python
from financial_analysis import StockInfo
from financial_analysis.crawl4ai_news_crawler import StockNewsCrawlAnalyzer

# 初始化分析器
analyzer = StockNewsCrawlAnalyzer()

# 创建股票信息
stock_info = StockInfo(
    symbol="000001",
    name="平安银行",
    exchange="SZSE",
    sector="金融"
)

# 获取分析后的新闻
analyzed_news = await analyzer.get_analyzed_stock_news(
    stock_info=stock_info,
    max_news_per_source=10,
    min_value_score=70.0
)

# 生成报告
report = await analyzer.generate_news_report(stock_info, analyzed_news)
print(report)
```

### 高级用法

```python
# 自定义新闻源
custom_sources = ["sina_finance", "eastmoney"]

# 获取新闻
news_items = await analyzer.get_analyzed_stock_news(
    stock_info=stock_info,
    max_news_per_source=5,
    sources=custom_sources,
    min_value_score=60.0
)

# 筛选高影响新闻
high_impact_news = [
    news for news in news_items 
    if news.impact_level == "high"
]
```

## 环境配置

### 1. Crawl4AI 服务部署

```bash
# 使用 Docker 部署 Crawl4AI
docker run -d \
  -p 11235:11235 \
  --name crawl4ai \
  --shm-size=1g \
  unclecode/crawl4ai:latest

# 验证服务状态
curl http://localhost:11235/health
```

### 2. Windmill 配置

确保以下环境变量已设置：

```bash
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your-token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=text_generation
```

### 3. Python 依赖

```bash
pip install aiohttp loguru
```

## 性能优化

### 1. 并发控制
- 批量处理新闻验证（每批5条）
- 异步爬取多个新闻源
- 智能延时避免API限制

### 2. 缓存策略
- 内存缓存验证结果
- 去重算法减少重复处理
- 时间窗口内的结果复用

### 3. 错误处理
- 自动重试机制
- 优雅降级策略
- 详细的错误日志

## 监控和调试

### 1. 日志配置

```python
from loguru import logger

# 配置详细日志
logger.add(
    "crawl4ai_news.log",
    level="DEBUG",
    format="{time} | {level} | {name}:{function}:{line} - {message}",
    rotation="1 day"
)
```

### 2. 性能指标

- 爬取成功率
- 验证准确率
- 平均响应时间
- API调用频率

### 3. 健康检查

```python
# 检查 Crawl4AI 服务
async with Crawl4aiNewsClient() as client:
    is_healthy = await client.health_check()

# 检查 Windmill 服务
from financial_analysis.windmill_client import windmill_client
result = await windmill_client.generate_text_analysis("test", "test")
```

## 扩展建议

### 1. 新闻源扩展
- 添加更多财经媒体
- 支持国际新闻源
- 集成社交媒体数据

### 2. 分析算法优化
- 机器学习模型训练
- 历史数据回测
- 实时情感监控

### 3. 功能增强
- 新闻推送服务
- 实时监控面板
- 移动端适配

## 故障排除

### 常见问题

1. **Crawl4AI 连接失败**
   - 检查服务是否启动
   - 验证端口配置
   - 查看防火墙设置

2. **新闻爬取失败**
   - 检查目标网站可访问性
   - 验证CSS选择器
   - 调整请求频率

3. **Windmill 分析失败**
   - 检查API配额
   - 验证认证信息
   - 调整提示词格式

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 单步调试
crawler = StockNewsCrawler()
news_items = await crawler.crawl_stock_news(stock_info, max_news_per_source=1)

# 验证单条新闻
validator = NewsValidator()
result = await validator._process_news_batch([news_items[0]], stock_info)
```

## 最佳实践

1. **合理设置爬取频率**: 避免对目标网站造成压力
2. **监控API配额**: 合理分配 Windmill 调用次数
3. **定期更新选择器**: 网站结构变化时及时调整
4. **数据质量检查**: 定期验证爬取结果的准确性
5. **错误处理**: 实现完善的异常处理和恢复机制

## 总结

本系统提供了一个完整的股票新闻爬取和分析解决方案，通过集成 Crawl4AI 和 Windmill 的强大功能，实现了智能化的新闻价值评估。系统具有良好的扩展性和可维护性，可以根据实际需求进行定制和优化。
