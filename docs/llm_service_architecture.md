# 统一LLM服务层架构文档

## 概述

统一LLM服务层是金融分析项目的核心增强功能，提供了对多种大语言模型提供商的统一调用接口。该服务层在保持与现有Windmill接口完全兼容的基础上，新增了多提供商支持、故障转移、限流控制、服务降级等企业级功能。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  分析引擎  │  新闻搜索  │  热点分析  │  其他业务模块        │
├─────────────────────────────────────────────────────────────┤
│                  兼容性适配层 (Compatibility Layer)          │
├─────────────────────────────────────────────────────────────┤
│              统一LLM服务管理器 (Unified LLM Manager)         │
├─────────────────────────────────────────────────────────────┤
│    限流控制    │    故障转移    │    服务降级    │    监控    │
├─────────────────────────────────────────────────────────────┤
│                  LLM提供商适配层 (Provider Adapters)         │
├─────────────────────────────────────────────────────────────┤
│  Windmill  │  OpenAI GPT  │  Google Gemini  │  阿里云千问   │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 数据模型层 (Data Models)
- **LLMRequest**: 统一的请求模型，支持所有LLM提供商的参数
- **LLMResponse**: 标准化的响应模型，包含内容、元数据和使用统计
- **LLMProviderConfig**: 提供商配置模型，支持灵活的参数配置
- **LLMError**: 统一的错误处理模型，支持错误分类和重试策略

#### 2. 服务抽象层 (Service Abstraction)
- **BaseLLMProvider**: 抽象基类，定义所有提供商必须实现的接口
- **LLMServiceRegistry**: 服务注册表，管理所有已注册的提供商
- **LLMServiceManager**: 服务管理器，提供统一的调用接口和故障转移

#### 3. 提供商适配层 (Provider Adapters)
- **WindmillLLMProvider**: Windmill适配器，保持向后兼容
- **OpenAILLMProvider**: OpenAI GPT适配器（预留接口）
- **GeminiLLMProvider**: Google Gemini适配器（预留接口）
- **QwenLLMProvider**: 阿里云通义千问适配器（预留接口）

#### 4. 控制和监控层 (Control & Monitoring)
- **LLMRateLimiter**: 限流控制器，基于令牌桶和滑动窗口算法
- **CircuitBreaker**: 熔断器，防止服务雪崩
- **LLMServiceDegrader**: 服务降级器，提供降级策略

## 核心特性

### 1. 多提供商支持
- 支持同时配置多个LLM提供商
- 基于优先级的智能路由
- 动态提供商注册和注销

### 2. 故障转移机制
- 自动检测提供商故障
- 智能切换到备用提供商
- 支持手动和自动故障恢复

### 3. 限流和控制
- 基于令牌桶的请求限流
- 支持按提供商独立限流
- 滑动窗口算法精确控制

### 4. 服务降级
- 熔断器模式防止雪崩
- 多级降级策略
- 自动服务恢复机制

### 5. 向后兼容
- 完全兼容现有Windmill接口
- 无缝迁移，零代码修改
- 渐进式功能增强

## 配置说明

### 环境变量配置

```bash
# 默认LLM提供商配置
DEFAULT_LLM_PROVIDER=windmill
LLM_FALLBACK_ENABLED=true
LLM_MAX_RETRIES=3
LLM_RETRY_DELAY=1.0
LLM_TIMEOUT=120

# Windmill配置（保持向后兼容）
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output
WINDMILL_ENABLED=true
WINDMILL_PRIORITY=1

# OpenAI配置
OPENAI_API_KEY=your_openai_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_ENABLED=false
OPENAI_PRIORITY=2

# Google Gemini配置
GEMINI_API_KEY=your_gemini_key
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1
GEMINI_ENABLED=false
GEMINI_PRIORITY=3

# 阿里云通义千问配置
QWEN_API_KEY=your_qwen_key
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-turbo
QWEN_ENABLED=false
QWEN_PRIORITY=4

# 模拟提供商配置（用于测试）
MOCK_LLM_ENABLED=false
MOCK_LLM_PRIORITY=999
```

### 代码配置示例

```python
from financial_analysis import unified_llm_service, LLMProviderFactory, llm_registry

# 创建自定义提供商
custom_provider = LLMProviderFactory.create_windmill_provider(
    provider_id="custom_windmill",
    provider_name="自定义Windmill",
    base_url="https://custom.windmill.com",
    api_key="custom_token",
    priority=1
)

# 注册提供商
llm_registry.register_provider(custom_provider)

# 设置默认提供商
unified_llm_service.llm_manager.set_default_provider("custom_windmill")
```

## 使用指南

### 基础使用

```python
from financial_analysis import unified_llm_service

# 简单文本生成（兼容现有接口）
async def basic_usage():
    result = await unified_llm_service.generate_text_analysis(
        prompt="请分析苹果公司的投资价值",
        system_instruction="你是专业的金融分析师",
        search=True
    )
    print(result)

# 带元数据的文本生成
async def advanced_usage():
    response = await unified_llm_service.generate_text_with_metadata(
        prompt="请分析苹果公司的投资价值",
        system_instruction="你是专业的金融分析师",
        search=True,
        temperature=0.7,
        max_tokens=1000
    )
    
    print(f"内容: {response.content}")
    print(f"提供商: {response.provider}")
    print(f"模型: {response.model}")
    print(f"Token使用: {response.usage.total_tokens}")
    print(f"响应时间: {response.response_time}秒")
```

### 指定提供商使用

```python
# 使用特定提供商
async def specific_provider():
    result = await unified_llm_service.generate_text_analysis(
        prompt="分析市场趋势",
        provider_id="windmill"  # 指定使用Windmill
    )
    return result

# 禁用故障转移
async def no_fallback():
    from financial_analysis import llm_manager
    
    response = await llm_manager.generate_text(
        request=LLMRequest(prompt="分析市场趋势"),
        provider_id="windmill",
        fallback=False  # 禁用故障转移
    )
    return response
```

### 健康检查和监控

```python
# 检查服务健康状态
async def health_monitoring():
    # 检查所有提供商
    health_status = await unified_llm_service.health_check()
    print(f"健康状态: {health_status}")
    
    # 获取服务统计
    stats = unified_llm_service.get_stats()
    print(f"服务统计: {stats}")
    
    # 获取可用提供商
    providers = unified_llm_service.get_available_providers()
    print(f"可用提供商: {providers}")
```

### 兼容性使用

```python
from financial_analysis.llm_compatibility import enhanced_windmill_client

# 完全兼容现有Windmill接口
async def compatibility_usage():
    # 使用增强的Windmill客户端（底层使用统一LLM服务）
    result = await enhanced_windmill_client.generate_text_analysis(
        prompt="分析股票趋势",
        system_instruction="你是金融专家",
        search=True
    )
    
    # 批量分析（保持接口兼容）
    news_items = [{"title": "新闻标题", "content": "新闻内容"}]
    stock_info = {"symbol": "AAPL", "name": "苹果公司"}
    
    relevance_results = await enhanced_windmill_client.batch_analyze_stock_relevance(
        news_items, stock_info
    )
    
    return result, relevance_results
```

## 错误处理

### 异常类型

```python
from financial_analysis.llm_service import (
    LLMServiceError,      # 通用LLM服务异常
    RateLimitError,       # 速率限制异常
    ValidationError,      # 参数验证异常
    ModelNotFoundError    # 模型未找到异常
)

# 异常处理示例
async def error_handling():
    try:
        result = await unified_llm_service.generate_text_analysis(
            prompt="分析请求"
        )
    except RateLimitError as e:
        print(f"速率限制: {e.message}, 重试间隔: {e.retry_after}秒")
    except ValidationError as e:
        print(f"参数验证失败: {e.message}")
    except LLMServiceError as e:
        print(f"LLM服务错误: {e.message}, 可重试: {e.is_retryable}")
    except Exception as e:
        print(f"未知错误: {str(e)}")
```

## 性能优化

### 1. 连接池管理
- 自动管理HTTP连接池
- 支持连接复用和超时控制
- 异步非阻塞调用

### 2. 缓存策略
- 支持响应结果缓存
- 智能缓存失效机制
- 减少重复请求开销

### 3. 并发控制
- 支持并发请求处理
- 智能负载均衡
- 防止资源过度消耗

## 监控和日志

### 日志记录
- 详细的请求/响应日志
- 性能指标记录
- 错误追踪和分析

### 监控指标
- 请求成功率
- 平均响应时间
- Token使用统计
- 提供商健康状态

### 告警机制
- 服务异常告警
- 性能阈值告警
- 资源使用告警

## 扩展开发

### 添加新的LLM提供商

1. 实现提供商适配器：
```python
from financial_analysis.llm_service import BaseLLMProvider

class CustomLLMProvider(BaseLLMProvider):
    async def generate_text(self, request):
        # 实现文本生成逻辑
        pass
    
    async def validate_request(self, request):
        # 实现请求验证逻辑
        pass
    
    # 实现其他必需方法...
```

2. 注册提供商类型：
```python
from financial_analysis.llm_providers import LLMProviderFactory

LLMProviderFactory.register_provider_class("custom", CustomLLMProvider)
```

3. 配置和使用：
```python
config = LLMProviderConfig(
    provider_id="custom",
    provider_type="custom",
    # 其他配置...
)

provider = LLMProviderFactory.create_provider(config)
llm_registry.register_provider(provider)
```

## 最佳实践

### 1. 提供商选择
- 根据任务类型选择合适的提供商
- 考虑成本、性能和可靠性平衡
- 设置合理的优先级和故障转移策略

### 2. 参数调优
- 根据具体场景调整temperature等参数
- 合理设置max_tokens避免截断
- 使用system_instruction提高输出质量

### 3. 错误处理
- 实现完善的异常处理机制
- 设置合理的重试策略
- 记录详细的错误日志

### 4. 性能优化
- 启用适当的缓存策略
- 合理设置并发限制
- 监控和优化响应时间

## 故障排除

### 常见问题

1. **提供商初始化失败**
   - 检查API密钥配置
   - 验证网络连接
   - 查看详细错误日志

2. **请求超时**
   - 调整timeout配置
   - 检查网络延迟
   - 考虑使用故障转移

3. **速率限制**
   - 调整限流配置
   - 分散请求时间
   - 使用多个提供商

4. **响应质量问题**
   - 优化prompt设计
   - 调整模型参数
   - 使用更适合的模型

### 调试工具

```python
from financial_analysis.llm_compatibility import (
    get_llm_service_status, 
    test_llm_service
)

# 获取服务状态
status = get_llm_service_status()
print(status)

# 运行服务测试
test_results = await test_llm_service()
print(test_results)
```

## 总结

统一LLM服务层为金融分析项目提供了强大而灵活的大语言模型调用能力。通过统一的接口、智能的故障转移、完善的监控和向后兼容性，该服务层不仅提升了系统的可靠性和性能，还为未来的扩展和优化奠定了坚实的基础。

主要优势：
- **统一接口**: 简化多提供商管理
- **高可用性**: 故障转移和服务降级
- **向后兼容**: 无缝迁移现有代码
- **可扩展性**: 易于添加新的提供商
- **企业级**: 限流、监控、日志等完善功能

通过合理的配置和使用，该服务层能够显著提升金融分析系统的智能化水平和用户体验。
