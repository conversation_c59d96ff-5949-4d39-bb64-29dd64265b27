#!/usr/bin/env python3
"""
Crawl4AI 股票新闻系统使用示例

展示如何在实际项目中使用 Crawl4AI 股票新闻爬取和分析系统
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import StockInfo
from financial_analysis.crawl4ai_news_crawler import StockNewsCrawlAnalyzer


async def example_single_stock_analysis():
    """示例：单个股票新闻分析"""
    print("📊 示例：单个股票新闻分析")
    print("-" * 40)
    
    # 创建股票信息
    stock_info = StockInfo(
        symbol="000001",
        name="平安银行",
        exchange="SZSE",
        sector="金融"
    )
    
    # 初始化分析器
    analyzer = StockNewsCrawlAnalyzer()
    
    try:
        print(f"正在分析 {stock_info.name} ({stock_info.symbol}) 的最新新闻...")
        
        # 获取分析后的新闻
        analyzed_news = await analyzer.get_analyzed_stock_news(
            stock_info=stock_info,
            max_news_per_source=5,
            min_value_score=60.0
        )
        
        if analyzed_news:
            print(f"✅ 找到 {len(analyzed_news)} 条高价值新闻")
            
            # 显示前3条新闻
            for i, news in enumerate(analyzed_news[:3], 1):
                print(f"\n[{i}] {news.title}")
                print(f"    价值评分: {news.value_score:.0f}/100")
                print(f"    影响程度: {news.impact_level}")
                print(f"    情感倾向: {news.sentiment}")
                print(f"    来源: {news.source}")
                print(f"    分析: {news.analysis_summary}")
            
            # 生成详细报告
            report = await analyzer.generate_news_report(stock_info, analyzed_news)
            print(f"\n📄 详细报告:")
            print(report)
            
        else:
            print("❌ 未找到高价值新闻")
            
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")


async def example_multiple_stocks_comparison():
    """示例：多个股票新闻对比分析"""
    print("\n📈 示例：多个股票新闻对比分析")
    print("-" * 40)
    
    # 定义要分析的股票列表
    stocks = [
        StockInfo(symbol="000001", name="平安银行", exchange="SZSE", sector="金融"),
        StockInfo(symbol="000002", name="万科A", exchange="SZSE", sector="房地产"),
        StockInfo(symbol="600036", name="招商银行", exchange="SSE", sector="金融")
    ]
    
    analyzer = StockNewsCrawlAnalyzer()
    
    # 存储所有分析结果
    all_results = {}
    
    for stock_info in stocks:
        try:
            print(f"\n正在分析 {stock_info.name} ({stock_info.symbol})...")
            
            analyzed_news = await analyzer.get_analyzed_stock_news(
                stock_info=stock_info,
                max_news_per_source=3,
                min_value_score=50.0
            )
            
            all_results[stock_info.symbol] = {
                'stock_info': stock_info,
                'news_count': len(analyzed_news),
                'avg_value_score': sum(news.value_score or 0 for news in analyzed_news) / len(analyzed_news) if analyzed_news else 0,
                'sentiment_distribution': _calculate_sentiment_distribution(analyzed_news),
                'top_news': analyzed_news[:2] if analyzed_news else []
            }
            
            print(f"  ✅ {len(analyzed_news)} 条新闻，平均价值评分: {all_results[stock_info.symbol]['avg_value_score']:.1f}")
            
        except Exception as e:
            print(f"  ❌ 分析失败: {str(e)}")
            all_results[stock_info.symbol] = None
    
    # 生成对比报告
    print(f"\n📊 对比分析报告:")
    print("=" * 60)
    
    for symbol, result in all_results.items():
        if result:
            stock_name = result['stock_info'].name
            news_count = result['news_count']
            avg_score = result['avg_value_score']
            sentiment = result['sentiment_distribution']
            
            print(f"\n{stock_name} ({symbol}):")
            print(f"  新闻数量: {news_count}")
            print(f"  平均价值: {avg_score:.1f}/100")
            print(f"  情感分布: 正面{sentiment['positive']}条, 中性{sentiment['neutral']}条, 负面{sentiment['negative']}条")
            
            if result['top_news']:
                print(f"  重要新闻: {result['top_news'][0].title[:50]}...")


def _calculate_sentiment_distribution(news_list):
    """计算情感分布"""
    distribution = {'positive': 0, 'negative': 0, 'neutral': 0}
    
    for news in news_list:
        sentiment = news.sentiment or 'neutral'
        if sentiment in distribution:
            distribution[sentiment] += 1
    
    return distribution


async def example_custom_configuration():
    """示例：自定义配置使用"""
    print("\n🔧 示例：自定义配置使用")
    print("-" * 40)
    
    stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
    
    # 使用自定义 Crawl4AI 服务地址
    custom_analyzer = StockNewsCrawlAnalyzer(crawl4ai_url="http://localhost:11235")
    
    try:
        # 只使用特定新闻源
        analyzed_news = await custom_analyzer.get_analyzed_stock_news(
            stock_info=stock_info,
            max_news_per_source=2,
            sources=["sina_finance", "eastmoney"],  # 只使用这两个源
            min_value_score=70.0  # 更高的价值阈值
        )
        
        print(f"✅ 自定义配置分析完成: {len(analyzed_news)} 条高价值新闻")
        
        # 展示配置效果
        if analyzed_news:
            sources_used = set(news.source for news in analyzed_news)
            print(f"使用的新闻源: {', '.join(sources_used)}")
            
            high_value_count = sum(1 for news in analyzed_news if news.value_score >= 80)
            print(f"超高价值新闻(≥80分): {high_value_count} 条")
        
    except Exception as e:
        print(f"❌ 自定义配置分析失败: {str(e)}")


async def example_real_time_monitoring():
    """示例：实时监控模拟"""
    print("\n⏰ 示例：实时监控模拟")
    print("-" * 40)
    
    # 监控的股票列表
    watch_list = [
        StockInfo(symbol="000001", name="平安银行", exchange="SZSE"),
        StockInfo(symbol="600036", name="招商银行", exchange="SSE")
    ]
    
    analyzer = StockNewsCrawlAnalyzer()
    
    print("开始模拟实时监控（运行3轮）...")
    
    for round_num in range(1, 4):
        print(f"\n--- 第 {round_num} 轮监控 ({datetime.now().strftime('%H:%M:%S')}) ---")
        
        for stock_info in watch_list:
            try:
                # 获取最新新闻
                analyzed_news = await analyzer.get_analyzed_stock_news(
                    stock_info=stock_info,
                    max_news_per_source=1,  # 每次只获取最新的
                    min_value_score=60.0
                )
                
                if analyzed_news:
                    latest_news = analyzed_news[0]
                    print(f"🔔 {stock_info.name}: {latest_news.title[:40]}... (价值:{latest_news.value_score:.0f})")
                else:
                    print(f"📰 {stock_info.name}: 暂无新的高价值新闻")
                    
            except Exception as e:
                print(f"❌ {stock_info.name} 监控失败: {str(e)}")
        
        # 模拟等待间隔
        if round_num < 3:
            print("等待下一轮监控...")
            await asyncio.sleep(2)  # 实际应用中可能是几分钟或更长


async def example_integration_with_existing_system():
    """示例：与现有系统集成"""
    print("\n🔗 示例：与现有系统集成")
    print("-" * 40)
    
    # 模拟现有系统的股票列表
    portfolio_stocks = ["000001", "000002", "600036", "600519"]
    
    analyzer = StockNewsCrawlAnalyzer()
    
    # 为投资组合中的每只股票获取新闻摘要
    portfolio_news_summary = {}
    
    for symbol in portfolio_stocks:
        try:
            # 简化的股票信息（实际应用中可能从数据库获取）
            stock_info = StockInfo(symbol=symbol, name=f"股票{symbol}", exchange="SZSE")
            
            # 获取新闻摘要
            analyzed_news = await analyzer.get_analyzed_stock_news(
                stock_info=stock_info,
                max_news_per_source=2,
                min_value_score=50.0
            )
            
            if analyzed_news:
                # 计算综合评分
                avg_value = sum(news.value_score or 0 for news in analyzed_news) / len(analyzed_news)
                sentiment_score = _calculate_sentiment_score(analyzed_news)
                
                portfolio_news_summary[symbol] = {
                    'news_count': len(analyzed_news),
                    'avg_value_score': avg_value,
                    'sentiment_score': sentiment_score,
                    'alert_level': 'high' if avg_value > 80 else 'medium' if avg_value > 60 else 'low'
                }
            else:
                portfolio_news_summary[symbol] = {
                    'news_count': 0,
                    'avg_value_score': 0,
                    'sentiment_score': 0,
                    'alert_level': 'none'
                }
                
        except Exception as e:
            print(f"❌ 处理 {symbol} 失败: {str(e)}")
    
    # 生成投资组合新闻报告
    print("📋 投资组合新闻摘要:")
    for symbol, summary in portfolio_news_summary.items():
        alert_emoji = "🔴" if summary['alert_level'] == 'high' else "🟡" if summary['alert_level'] == 'medium' else "🟢"
        print(f"{alert_emoji} {symbol}: {summary['news_count']}条新闻, 价值{summary['avg_value_score']:.0f}, 情感{summary['sentiment_score']:+.1f}")


def _calculate_sentiment_score(news_list):
    """计算情感评分"""
    if not news_list:
        return 0
    
    sentiment_values = {'positive': 1, 'neutral': 0, 'negative': -1}
    total_score = sum(sentiment_values.get(news.sentiment, 0) for news in news_list)
    return total_score / len(news_list)


async def main():
    """主函数"""
    print("🚀 Crawl4AI 股票新闻系统使用示例")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n⚠️  注意：以下示例需要 Crawl4AI 服务运行在 localhost:11235")
    print("启动命令: docker run -d -p 11235:11235 --name crawl4ai unclecode/crawl4ai:latest")
    
    try:
        # 运行各种使用示例
        await example_single_stock_analysis()
        await example_multiple_stocks_comparison()
        await example_custom_configuration()
        await example_real_time_monitoring()
        await example_integration_with_existing_system()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  示例被用户中断")
    except Exception as e:
        print(f"\n\n❌ 示例运行中发生错误: {str(e)}")
    
    print(f"\n✅ 示例完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n📚 更多用法:")
    print("1. 查看文档: docs/crawl4ai_news_integration.md")
    print("2. 运行测试: python test_crawl4ai_integration.py")
    print("3. 完整演示: python demo_crawl4ai_news.py")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
