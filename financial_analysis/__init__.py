"""
金融证券分析项目

本项目旨在利用人工智能技术，特别是大语言模型，来辅助进行金融证券分析。
通过构建一个基于大语言模型的分析工具，实现自动化分析、智能化报告生成等功能。

新增统一的LLM服务层，支持多种大语言模型提供商的统一调用，包括：
- Windmill（保持向后兼容）
- Google Gemini（预留接口）
- OpenAI GPT系列（预留接口）
- 阿里云通义千问（预留接口）
"""

__version__ = "0.1.0"
__author__ = "AIER Team"
__email__ = "<EMAIL>"

from .config import settings
from .models import (
    StockInfo, AnalysisReport, StockPrice, TechnicalIndicators, NewsItem,
    HotNewsItem, HotNewsChannel, HotNewsCache, UnifiedStockNews,
    # LLM服务层数据模型
    LLMRequest, LLMResponse, LLMStreamChunk, LLMError,
    LLMProviderConfig, LLMUsage, LLMStreamConfig,
    StreamCancellationToken, StreamProgress
)
from .stock_data import StockDataProvider
from .news_search import NewsSearcher
from .analysis import AnalysisEngine
from .stock_news_aggregator import StockNewsAggregator, stock_news_aggregator
from .crawl4ai_news_crawler import (
    Crawl4aiNewsClient, StockNewsCrawler, NewsValidator,
    StockNewsCrawlAnalyzer, CrawledNewsItem, stock_news_analyzer
)
from .hot_news_manager import HotNewsManager, hot_news_manager
from .hot_news_collector import HotNewsCollector
from .hot_news_analyzer import HotNewsAnalyzer
from .hot_news_pusher import HotNewsPusher
from .hot_news_cache import HotNewsCacheManager, cache_manager

# LLM服务层
from .llm_service import llm_registry, llm_manager
from .llm_manager import unified_llm_service
from .llm_providers import LLMProviderFactory
from .llm_rate_limiter import llm_rate_limiter, llm_degrader
from .llm_streaming import streaming_manager

__all__ = [
    # 配置
    "settings",

    # 数据模型
    "StockInfo",
    "StockPrice",
    "TechnicalIndicators",
    "NewsItem",
    "AnalysisReport",
    "HotNewsItem",
    "HotNewsChannel",
    "HotNewsCache",
    "UnifiedStockNews",
    "CrawledNewsItem",

    # LLM服务层数据模型
    "LLMRequest",
    "LLMResponse",
    "LLMStreamChunk",
    "LLMError",
    "LLMProviderConfig",
    "LLMUsage",
    "LLMStreamConfig",
    "StreamCancellationToken",
    "StreamProgress",

    # 核心服务
    "StockDataProvider",
    "NewsSearcher",
    "AnalysisEngine",
    "StockNewsAggregator",
    "stock_news_aggregator",
    "Crawl4aiNewsClient",
    "StockNewsCrawler",
    "NewsValidator",
    "StockNewsCrawlAnalyzer",
    "stock_news_analyzer",
    "HotNewsManager",
    "hot_news_manager",
    "HotNewsCollector",
    "HotNewsAnalyzer",
    "HotNewsPusher",
    "HotNewsCacheManager",
    "cache_manager",

    # LLM服务层
    "llm_registry",
    "llm_manager",
    "unified_llm_service",
    "LLMProviderFactory",
    "llm_rate_limiter",
    "llm_degrader",
    "streaming_manager",
]
