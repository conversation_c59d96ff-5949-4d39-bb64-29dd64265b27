"""
项目配置模块

负责管理项目的所有配置参数，包括API密钥、数据源配置、日志配置等。
使用pydantic-settings进行配置管理，支持从环境变量和.env文件读取配置。
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """项目配置类"""
    
    # 大模型配置（已迁移到Windmill生成文本接口）
    # 保留model配置用于兼容性，但实际使用Windmill接口
    gemini_model: str = Field(default="gemini-pro", description="大模型名称（仅用于标识）")
    
    # Windmill配置
    windmill_base_url: Optional[str] = Field(default=None, description="Windmill服务基础URL")
    windmill_token: Optional[str] = Field(default=None, description="Windmill访问令牌")
    windmill_workspace: str = Field(default="my_workspace", description="Windmill工作空间")
    windmill_folder: str = Field(default="gemini", description="Windmill文件夹")
    windmill_script: str = Field(default="js_structured_output", description="Windmill脚本名称")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="logs/financial_analysis.log", description="日志文件路径")
    
    # 数据源配置
    default_exchange: str = Field(default="SSE", description="默认交易所")
    data_cache_duration: int = Field(default=300, description="数据缓存时间（秒）")
    
    # 分析配置
    analysis_days: int = Field(default=30, description="分析的历史天数")
    news_search_days: int = Field(default=7, description="新闻搜索的天数范围")

    # 热点信息配置
    hot_news_enabled: bool = Field(default=True, description="是否启用热点信息功能")
    hot_news_fetch_interval: int = Field(default=300, description="热点信息获取间隔（秒）")
    hot_news_cache_duration: int = Field(default=1800, description="热点信息缓存时间（秒）")
    hot_news_max_items: int = Field(default=100, description="单次获取的最大新闻数量")
    hot_news_history_check_days: int = Field(default=3, description="历史信息检查天数")

    # 热点信息推送配置
    hot_news_push_enabled: bool = Field(default=True, description="是否启用热点信息推送")
    hot_news_push_interval: int = Field(default=600, description="推送间隔（秒）")
    hot_news_push_batch_size: int = Field(default=10, description="单次推送的最大数量")
    hot_news_min_importance: str = Field(default="medium", description="推送的最低重要程度：high/medium/low")

    # 热点信息数据源配置
    hot_news_channels: str = Field(default="", description="热点信息数据源配置（JSON格式）")

    # ==================== LLM 服务层配置 ====================

    # 默认LLM提供商配置
    default_llm_provider: str = Field(default="windmill", description="默认LLM提供商ID")
    llm_fallback_enabled: bool = Field(default=True, description="是否启用LLM故障转移")
    llm_max_retries: int = Field(default=3, description="LLM请求最大重试次数")
    llm_retry_delay: float = Field(default=1.0, description="LLM重试延迟（秒）")
    llm_timeout: int = Field(default=120, description="LLM请求超时时间（秒）")

    # OpenAI配置
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(default=None, description="OpenAI API基础URL")
    openai_model: str = Field(default="gpt-3.5-turbo", description="OpenAI默认模型")
    openai_enabled: bool = Field(default=False, description="是否启用OpenAI提供商")
    openai_priority: int = Field(default=2, description="OpenAI提供商优先级")

    # Google Gemini配置
    gemini_api_key: Optional[str] = Field(default=None, description="Google Gemini API密钥")
    gemini_base_url: Optional[str] = Field(default=None, description="Gemini API基础URL")
    gemini_enabled: bool = Field(default=False, description="是否启用Gemini提供商")
    gemini_priority: int = Field(default=3, description="Gemini提供商优先级")

    # 阿里云通义千问配置
    qwen_api_key: Optional[str] = Field(default=None, description="通义千问API密钥")
    qwen_base_url: Optional[str] = Field(default=None, description="通义千问API基础URL")
    qwen_model: str = Field(default="qwen-turbo", description="通义千问默认模型")
    qwen_enabled: bool = Field(default=False, description="是否启用通义千问提供商")
    qwen_priority: int = Field(default=4, description="通义千问提供商优先级")

    # Windmill提供商配置（保持向后兼容）
    windmill_enabled: bool = Field(default=True, description="是否启用Windmill提供商")
    windmill_priority: int = Field(default=1, description="Windmill提供商优先级")

    # 模拟提供商配置（用于测试）
    mock_llm_enabled: bool = Field(default=False, description="是否启用模拟LLM提供商")
    mock_llm_priority: int = Field(default=999, description="模拟LLM提供商优先级")

    # 流式响应配置
    stream_enabled: bool = Field(default=True, description="是否启用流式响应功能")
    stream_chunk_size: int = Field(default=50, description="流式响应块大小（字符数）")
    stream_chunk_delay: float = Field(default=0.1, description="流式响应块之间的延迟（秒）")
    stream_timeout: int = Field(default=300, description="流式响应超时时间（秒）")
    stream_buffer_size: int = Field(default=1024, description="流式响应缓冲区大小")
    stream_split_by_sentence: bool = Field(default=True, description="是否按句子分割流式响应")
    stream_enable_progress: bool = Field(default=True, description="是否启用流式响应进度报告")
    stream_enable_cancellation: bool = Field(default=True, description="是否启用流式响应取消功能")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
