"""
基于 Crawl4AI 的股票新闻爬取器

使用 Crawl4AI HTTP 接口爬取特定股票的最新新闻，
然后通过 Windmill 的 text_generation 接口验证新闻新鲜度并分析价值。
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
import aiohttp
from loguru import logger

from .windmill_client import windmill_client
from .models import StockInfo, NewsItem
from .config import settings


@dataclass
class CrawledNewsItem:
    """爬取的新闻条目"""
    title: str
    content: str
    url: str
    source: str
    publish_time: Optional[datetime] = None
    raw_html: Optional[str] = None
    
    # 验证和分析结果
    is_fresh: Optional[bool] = None
    freshness_score: Optional[float] = None
    value_score: Optional[float] = None
    impact_level: Optional[str] = None
    sentiment: Optional[str] = None
    analysis_summary: Optional[str] = None


class Crawl4aiNewsClient:
    """Crawl4AI HTTP 客户端"""
    
    def __init__(self, base_url: str = "http://*************:11235"):
        """
        初始化 Crawl4AI 客户端
        
        Args:
            base_url: Crawl4AI 服务器地址
        """
        self.base_url = base_url.rstrip('/')
        self.session = None
        logger.info(f"Crawl4AI 客户端初始化: {self.base_url}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=120)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def crawl_url(self, url: str, extraction_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        爬取单个 URL
        
        Args:
            url: 要爬取的 URL
            extraction_config: 提取配置
            
        Returns:
            爬取结果
        """
        try:
            # 构建请求配置
            browser_config = {
                "type": "BrowserConfig",
                "params": {
                    "headless": True,
                    "viewport": {
                        "type": "dict",
                        "value": {"width": 1920, "height": 1080}
                    }
                }
            }
            
            crawler_config = {
                "type": "CrawlerRunConfig", 
                "params": {
                    "stream": False,
                    "cache_mode": "bypass",
                    "word_count_threshold": 50,
                    "only_text": False
                }
            }
            
            # 添加提取策略
            if extraction_config:
                crawler_config["params"]["extraction_strategy"] = extraction_config
            
            payload = {
                "urls": [url],
                "browser_config": browser_config,
                "crawler_config": crawler_config
            }
            
            logger.debug(f"爬取 URL: {url}")
            
            async with self.session.post(
                f"{self.base_url}/crawl",
                json=payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    logger.debug(f"爬取成功: {url}")
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"爬取失败 {url}: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"爬取异常 {url}: {str(e)}")
            return None
    
    async def health_check(self) -> bool:
        """检查 Crawl4AI 服务健康状态"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return False


class StockNewsCrawler:
    """股票新闻爬取器"""
    
    def __init__(self, crawl4ai_url: str = "http://*************:11235"):
        """
        初始化股票新闻爬取器
        
        Args:
            crawl4ai_url: Crawl4AI 服务器地址
        """
        self.crawl4ai_url = crawl4ai_url
        self.news_sources = self._get_news_sources()
        logger.info("股票新闻爬取器初始化完成")
    
    def _get_news_sources(self) -> Dict[str, Dict[str, Any]]:
        """获取新闻源配置"""
        return {
            "sina_finance": {
                "name": "新浪财经",
                "search_url_template": "https://search.sina.com.cn/?q={stock_name}&range=all&c=news&sort=time",
                "extraction_config": {
                    "type": "JsonCssExtractionStrategy",
                    "params": {
                        "schema": {
                            "type": "dict",
                            "value": {
                                "baseSelector": ".result",
                                "fields": [
                                    {"name": "title", "selector": "h2 a", "type": "text"},
                                    {"name": "url", "selector": "h2 a", "type": "attribute", "attribute": "href"},
                                    {"name": "summary", "selector": ".content", "type": "text"},
                                    {"name": "time", "selector": ".time", "type": "text"}
                                ]
                            }
                        }
                    }
                }
            },
            "eastmoney": {
                "name": "东方财富",
                "search_url_template": "https://so.eastmoney.com/news/s?keyword={stock_name}",
                "extraction_config": {
                    "type": "JsonCssExtractionStrategy", 
                    "params": {
                        "schema": {
                            "type": "dict",
                            "value": {
                                "baseSelector": ".news-item",
                                "fields": [
                                    {"name": "title", "selector": ".news-title a", "type": "text"},
                                    {"name": "url", "selector": ".news-title a", "type": "attribute", "attribute": "href"},
                                    {"name": "summary", "selector": ".news-desc", "type": "text"},
                                    {"name": "time", "selector": ".news-time", "type": "text"}
                                ]
                            }
                        }
                    }
                }
            },
            "cnstock": {
                "name": "中国证券网",
                "search_url_template": "https://search.cnstock.com/search/news?searchword={stock_name}",
                "extraction_config": {
                    "type": "JsonCssExtractionStrategy",
                    "params": {
                        "schema": {
                            "type": "dict", 
                            "value": {
                                "baseSelector": ".search-result-item",
                                "fields": [
                                    {"name": "title", "selector": ".title a", "type": "text"},
                                    {"name": "url", "selector": ".title a", "type": "attribute", "attribute": "href"},
                                    {"name": "summary", "selector": ".summary", "type": "text"},
                                    {"name": "time", "selector": ".time", "type": "text"}
                                ]
                            }
                        }
                    }
                }
            }
        }
    
    async def crawl_stock_news(self, stock_info: StockInfo, 
                             max_news_per_source: int = 10,
                             sources: List[str] = None) -> List[CrawledNewsItem]:
        """
        爬取指定股票的最新新闻
        
        Args:
            stock_info: 股票信息
            max_news_per_source: 每个源的最大新闻数量
            sources: 指定的新闻源列表，None 表示使用所有源
            
        Returns:
            爬取的新闻列表
        """
        try:
            if sources is None:
                sources = list(self.news_sources.keys())
            
            logger.info(f"开始爬取股票新闻: {stock_info.name} ({stock_info.symbol})")
            
            all_news = []
            
            async with Crawl4aiNewsClient(self.crawl4ai_url) as client:
                # 检查服务健康状态
                if not await client.health_check():
                    logger.error("Crawl4AI 服务不可用")
                    return []
                
                # 并行爬取各个新闻源
                tasks = []
                for source_key in sources:
                    if source_key in self.news_sources:
                        task = self._crawl_single_source(
                            client, stock_info, source_key, max_news_per_source
                        )
                        tasks.append(task)
                
                # 等待所有任务完成
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"爬取源 {sources[i]} 失败: {str(result)}")
                    elif result:
                        all_news.extend(result)
            
            # 去重和排序
            unique_news = self._deduplicate_news(all_news)
            sorted_news = sorted(unique_news, key=lambda x: x.publish_time or datetime.min, reverse=True)
            
            logger.info(f"爬取完成: {stock_info.symbol}, 共 {len(sorted_news)} 条新闻")
            return sorted_news
            
        except Exception as e:
            logger.error(f"爬取股票新闻失败: {str(e)}")
            return []
    
    async def _crawl_single_source(self, client: Crawl4aiNewsClient, 
                                 stock_info: StockInfo, source_key: str,
                                 max_news: int) -> List[CrawledNewsItem]:
        """爬取单个新闻源"""
        try:
            source_config = self.news_sources[source_key]
            
            # 构建搜索 URL
            search_url = source_config["search_url_template"].format(
                stock_name=stock_info.name,
                stock_symbol=stock_info.symbol
            )
            
            logger.debug(f"爬取新闻源: {source_config['name']} - {search_url}")
            
            # 爬取搜索结果页面
            result = await client.crawl_url(search_url, source_config["extraction_config"])
            
            if not result or not result.get("success"):
                logger.warning(f"爬取失败: {source_config['name']}")
                return []
            
            # 解析提取的新闻数据
            news_items = []
            extracted_data = result.get("extracted_content", [])
            
            if isinstance(extracted_data, list):
                for item in extracted_data[:max_news]:
                    try:
                        news_item = self._parse_news_item(item, source_config["name"])
                        if news_item:
                            news_items.append(news_item)
                    except Exception as e:
                        logger.warning(f"解析新闻条目失败: {str(e)}")
            
            logger.debug(f"从 {source_config['name']} 获取 {len(news_items)} 条新闻")
            return news_items
            
        except Exception as e:
            logger.error(f"爬取源 {source_key} 异常: {str(e)}")
            return []
    
    def _parse_news_item(self, raw_item: Dict[str, Any], source: str) -> Optional[CrawledNewsItem]:
        """解析原始新闻条目"""
        try:
            title = raw_item.get("title", "").strip()
            url = raw_item.get("url", "").strip()
            summary = raw_item.get("summary", "").strip()
            time_str = raw_item.get("time", "").strip()
            
            if not title or not url:
                return None
            
            # 解析时间
            publish_time = self._parse_time(time_str)
            
            # 创建新闻条目
            news_item = CrawledNewsItem(
                title=title,
                content=summary,
                url=url,
                source=source,
                publish_time=publish_time
            )
            
            return news_item
            
        except Exception as e:
            logger.warning(f"解析新闻条目失败: {str(e)}")
            return None
    
    def _parse_time(self, time_str: str) -> Optional[datetime]:
        """解析时间字符串"""
        if not time_str:
            return None
        
        try:
            # 常见时间格式
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y-%m-%d",
                "%m-%d %H:%M",
                "%H:%M"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            # 处理相对时间
            if "分钟前" in time_str:
                minutes = int(time_str.replace("分钟前", ""))
                return datetime.now() - timedelta(minutes=minutes)
            elif "小时前" in time_str:
                hours = int(time_str.replace("小时前", ""))
                return datetime.now() - timedelta(hours=hours)
            elif "天前" in time_str:
                days = int(time_str.replace("天前", ""))
                return datetime.now() - timedelta(days=days)
            
            return None
            
        except Exception:
            return None
    
    def _deduplicate_news(self, news_list: List[CrawledNewsItem]) -> List[CrawledNewsItem]:
        """新闻去重"""
        seen_titles = set()
        unique_news = []
        
        for news in news_list:
            # 简化标题用于去重
            simplified_title = news.title.lower().replace(" ", "").replace("　", "")
            
            if simplified_title not in seen_titles:
                seen_titles.add(simplified_title)
                unique_news.append(news)
        
        return unique_news


class NewsValidator:
    """新闻验证和价值分析器"""

    def __init__(self):
        """初始化新闻验证器"""
        self.cache = {}  # 简单的内存缓存
        logger.info("新闻验证器初始化完成")

    async def validate_and_analyze_news(self, news_items: List[CrawledNewsItem],
                                      stock_info: StockInfo) -> List[CrawledNewsItem]:
        """
        验证新闻新鲜度并分析价值

        Args:
            news_items: 爬取的新闻列表
            stock_info: 股票信息

        Returns:
            验证和分析后的新闻列表
        """
        try:
            logger.info(f"开始验证和分析 {len(news_items)} 条新闻")

            # 批量处理新闻
            batch_size = 5  # 每批处理5条新闻
            processed_news = []

            for i in range(0, len(news_items), batch_size):
                batch = news_items[i:i + batch_size]

                # 处理当前批次
                batch_results = await self._process_news_batch(batch, stock_info)
                processed_news.extend(batch_results)

                # 避免过于频繁的API调用
                if i + batch_size < len(news_items):
                    await asyncio.sleep(1)

            # 过滤和排序
            valid_news = [news for news in processed_news if news.is_fresh]
            sorted_news = sorted(valid_news, key=lambda x: x.value_score or 0, reverse=True)

            logger.info(f"验证完成: {len(valid_news)} 条新鲜新闻")
            return sorted_news

        except Exception as e:
            logger.error(f"新闻验证失败: {str(e)}")
            return news_items

    async def _process_news_batch(self, news_batch: List[CrawledNewsItem],
                                stock_info: StockInfo) -> List[CrawledNewsItem]:
        """处理新闻批次"""
        try:
            # 构建批量验证提示词
            prompt = self._build_validation_prompt(news_batch, stock_info)

            # 调用 Windmill 进行分析
            analysis_result = await windmill_client.generate_text_analysis(
                prompt=prompt,
                system_instruction=self._get_validation_system_instruction(),
                search=False
            )

            if analysis_result:
                # 解析分析结果
                return self._parse_validation_result(analysis_result, news_batch)
            else:
                logger.warning("Windmill 分析失败，使用默认值")
                return self._apply_default_analysis(news_batch)

        except Exception as e:
            logger.error(f"处理新闻批次失败: {str(e)}")
            return self._apply_default_analysis(news_batch)

    def _build_validation_prompt(self, news_batch: List[CrawledNewsItem],
                               stock_info: StockInfo) -> str:
        """构建验证提示词"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        prompt = f"""请分析以下关于 {stock_info.name} ({stock_info.symbol}) 的新闻，评估每条新闻的新鲜度和投资价值。

当前时间：{current_time}
股票信息：
- 代码：{stock_info.symbol}
- 名称：{stock_info.name}
- 行业：{stock_info.sector or '未知'}

新闻列表：
"""

        for i, news in enumerate(news_batch, 1):
            publish_time_str = news.publish_time.strftime("%Y-%m-%d %H:%M:%S") if news.publish_time else "未知"
            prompt += f"""
[新闻 {i}]
标题：{news.title}
来源：{news.source}
发布时间：{publish_time_str}
内容摘要：{news.content[:200]}...
URL：{news.url}
"""

        prompt += """
请为每条新闻提供以下分析：

1. 新鲜度评估：
   - is_fresh: true/false (是否为24小时内的新闻或重要程度足以忽略时间)
   - freshness_score: 0-100 (新鲜度评分)

2. 价值分析：
   - value_score: 0-100 (对股票投资的价值评分)
   - impact_level: high/medium/low (对股价的潜在影响程度)
   - sentiment: positive/negative/neutral (情感倾向)

3. 分析摘要：
   - analysis_summary: 简要说明新闻的重要性和潜在影响

返回JSON格式：
{
  "analyses": [
    {
      "news_index": 1,
      "is_fresh": true,
      "freshness_score": 85,
      "value_score": 75,
      "impact_level": "medium",
      "sentiment": "positive",
      "analysis_summary": "公司发布季度财报，业绩超预期"
    }
  ]
}

评估标准：
- 新鲜度：优先考虑时间，但重大事件可忽略时间限制
- 价值评分：财报>重大公告>政策影响>行业动态>一般新闻
- 影响程度：考虑新闻对股价的直接和间接影响
- 情感分析：基于新闻内容对股票的正面/负面影响
"""

        return prompt

    def _get_validation_system_instruction(self) -> str:
        """获取验证系统指令"""
        return """你是一个专业的金融新闻分析师，具有丰富的股票投资和新闻价值评估经验。
请客观、准确地分析每条新闻的新鲜度和投资价值，重点关注：
1. 新闻的时效性和重要程度
2. 对股票价格的潜在影响
3. 市场情绪和投资者关注度
4. 信息的可靠性和权威性

分析要基于事实，避免主观臆断，为投资者提供有价值的参考信息。"""

    def _parse_validation_result(self, analysis_result: str,
                               news_batch: List[CrawledNewsItem]) -> List[CrawledNewsItem]:
        """解析验证结果"""
        try:
            # 尝试从结果中提取JSON
            import re

            # 查找JSON内容
            json_match = re.search(r'\{.*\}', analysis_result, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)

                analyses = data.get('analyses', [])

                # 应用分析结果
                for analysis in analyses:
                    news_index = analysis.get('news_index', 1) - 1
                    if 0 <= news_index < len(news_batch):
                        news = news_batch[news_index]
                        news.is_fresh = analysis.get('is_fresh', False)
                        news.freshness_score = analysis.get('freshness_score', 0)
                        news.value_score = analysis.get('value_score', 0)
                        news.impact_level = analysis.get('impact_level', 'low')
                        news.sentiment = analysis.get('sentiment', 'neutral')
                        news.analysis_summary = analysis.get('analysis_summary', '')

                return news_batch

        except Exception as e:
            logger.warning(f"解析验证结果失败: {str(e)}")

        # 解析失败时使用默认分析
        return self._apply_default_analysis(news_batch)

    def _apply_default_analysis(self, news_batch: List[CrawledNewsItem]) -> List[CrawledNewsItem]:
        """应用默认分析"""
        for news in news_batch:
            # 基于时间的简单新鲜度判断
            if news.publish_time:
                time_diff = datetime.now() - news.publish_time
                news.is_fresh = time_diff.total_seconds() < 24 * 3600  # 24小时内
                news.freshness_score = max(0, 100 - time_diff.total_seconds() / 3600)
            else:
                news.is_fresh = True  # 无时间信息时默认为新鲜
                news.freshness_score = 50

            # 默认价值评分
            news.value_score = 50
            news.impact_level = 'medium'
            news.sentiment = 'neutral'
            news.analysis_summary = '默认分析：需要进一步验证'

        return news_batch


class StockNewsCrawlAnalyzer:
    """股票新闻爬取分析器 - 整合爬取和分析功能"""

    def __init__(self, crawl4ai_url: str = "http://*************:11235"):
        """
        初始化股票新闻爬取分析器

        Args:
            crawl4ai_url: Crawl4AI 服务器地址
        """
        self.crawler = StockNewsCrawler(crawl4ai_url)
        self.validator = NewsValidator()
        logger.info("股票新闻爬取分析器初始化完成")

    async def get_analyzed_stock_news(self, stock_info: StockInfo,
                                    max_news_per_source: int = 10,
                                    sources: List[str] = None,
                                    min_value_score: float = 60.0) -> List[CrawledNewsItem]:
        """
        获取分析后的股票新闻

        Args:
            stock_info: 股票信息
            max_news_per_source: 每个源的最大新闻数量
            sources: 指定的新闻源
            min_value_score: 最小价值评分阈值

        Returns:
            分析后的高价值新闻列表
        """
        try:
            logger.info(f"开始获取和分析股票新闻: {stock_info.name} ({stock_info.symbol})")

            # 1. 爬取新闻
            crawled_news = await self.crawler.crawl_stock_news(
                stock_info, max_news_per_source, sources
            )

            if not crawled_news:
                logger.warning("未爬取到任何新闻")
                return []

            # 2. 验证和分析
            analyzed_news = await self.validator.validate_and_analyze_news(
                crawled_news, stock_info
            )

            # 3. 过滤高价值新闻
            high_value_news = [
                news for news in analyzed_news
                if news.value_score and news.value_score >= min_value_score
            ]

            logger.info(f"获取完成: {len(high_value_news)} 条高价值新闻")
            return high_value_news

        except Exception as e:
            logger.error(f"获取分析新闻失败: {str(e)}")
            return []

    async def generate_news_report(self, stock_info: StockInfo,
                                 analyzed_news: List[CrawledNewsItem]) -> str:
        """
        生成新闻分析报告

        Args:
            stock_info: 股票信息
            analyzed_news: 分析后的新闻列表

        Returns:
            新闻分析报告
        """
        try:
            if not analyzed_news:
                return f"未找到关于 {stock_info.name} ({stock_info.symbol}) 的高价值新闻。"

            # 统计信息
            total_news = len(analyzed_news)
            avg_value_score = sum(news.value_score or 0 for news in analyzed_news) / total_news

            sentiment_counts = {}
            impact_counts = {}

            for news in analyzed_news:
                sentiment = news.sentiment or 'neutral'
                impact = news.impact_level or 'medium'

                sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
                impact_counts[impact] = impact_counts.get(impact, 0) + 1

            # 构建报告
            report = f"""# {stock_info.name} ({stock_info.symbol}) 新闻分析报告

## 概览
- 总新闻数量: {total_news} 条
- 平均价值评分: {avg_value_score:.1f}/100
- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 情感分析
"""

            for sentiment, count in sentiment_counts.items():
                percentage = (count / total_news) * 100
                report += f"- {sentiment}: {count} 条 ({percentage:.1f}%)\n"

            report += "\n## 影响程度分布\n"
            for impact, count in impact_counts.items():
                percentage = (count / total_news) * 100
                report += f"- {impact}: {count} 条 ({percentage:.1f}%)\n"

            report += "\n## 重要新闻摘要\n"

            # 选择前5条最高价值的新闻
            top_news = sorted(analyzed_news, key=lambda x: x.value_score or 0, reverse=True)[:5]

            for i, news in enumerate(top_news, 1):
                report += f"""
### {i}. {news.title}
- **来源**: {news.source}
- **价值评分**: {news.value_score:.0f}/100
- **影响程度**: {news.impact_level}
- **情感倾向**: {news.sentiment}
- **发布时间**: {news.publish_time.strftime('%Y-%m-%d %H:%M') if news.publish_time else '未知'}
- **分析摘要**: {news.analysis_summary}
- **链接**: {news.url}
"""

            return report

        except Exception as e:
            logger.error(f"生成新闻报告失败: {str(e)}")
            return f"生成 {stock_info.name} 新闻报告失败。"


# 全局实例
stock_news_analyzer = StockNewsCrawlAnalyzer()
