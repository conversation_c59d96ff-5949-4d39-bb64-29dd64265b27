"""
数据模型定义

定义项目中使用的所有数据模型，包括股票信息、分析报告等。
使用pydantic进行数据验证和序列化。
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class StockInfo(BaseModel):
    """股票基本信息模型"""
    
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    exchange: str = Field(..., description="交易所")
    currency: str = Field(default="CNY", description="货币单位")
    sector: Optional[str] = Field(default=None, description="所属行业")
    market_cap: Optional[float] = Field(default=None, description="市值")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StockPrice(BaseModel):
    """股票价格数据模型"""
    
    symbol: str = Field(..., description="股票代码")
    date: datetime = Field(..., description="日期")
    open_price: float = Field(..., description="开盘价")
    high_price: float = Field(..., description="最高价")
    low_price: float = Field(..., description="最低价")
    close_price: float = Field(..., description="收盘价")
    volume: int = Field(..., description="成交量")
    turnover: Optional[float] = Field(default=None, description="成交额")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class NewsItem(BaseModel):
    """新闻条目模型"""
    
    title: str = Field(..., description="新闻标题")
    content: Optional[str] = Field(default=None, description="新闻内容")
    source: str = Field(..., description="新闻来源")
    publish_time: datetime = Field(..., description="发布时间")
    url: Optional[str] = Field(default=None, description="新闻链接")
    sentiment: Optional[str] = Field(default=None, description="情感倾向：positive/negative/neutral")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class TechnicalIndicators(BaseModel):
    """技术指标模型"""
    
    symbol: str = Field(..., description="股票代码")
    date: datetime = Field(..., description="计算日期")
    ma5: Optional[float] = Field(default=None, description="5日移动平均线")
    ma10: Optional[float] = Field(default=None, description="10日移动平均线")
    ma20: Optional[float] = Field(default=None, description="20日移动平均线")
    ma60: Optional[float] = Field(default=None, description="60日移动平均线")
    rsi: Optional[float] = Field(default=None, description="相对强弱指数")
    macd: Optional[float] = Field(default=None, description="MACD指标")
    kdj_k: Optional[float] = Field(default=None, description="KDJ指标K值")
    kdj_d: Optional[float] = Field(default=None, description="KDJ指标D值")
    kdj_j: Optional[float] = Field(default=None, description="KDJ指标J值")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AnalysisReport(BaseModel):
    """分析报告模型"""

    symbol: str = Field(..., description="股票代码")
    stock_info: StockInfo = Field(..., description="股票基本信息")
    analysis_date: datetime = Field(..., description="分析日期")

    # 价格数据分析
    current_price: float = Field(..., description="当前价格")
    price_change: float = Field(..., description="价格变化")
    price_change_percent: float = Field(..., description="价格变化百分比")

    # 技术分析
    technical_indicators: TechnicalIndicators = Field(..., description="技术指标")
    trend_analysis: str = Field(..., description="趋势分析")
    support_resistance: Dict[str, float] = Field(..., description="支撑位和阻力位")

    # 基本面分析
    fundamental_analysis: str = Field(..., description="基本面分析")

    # 新闻情感分析
    news_items: List[NewsItem] = Field(default_factory=list, description="相关新闻")
    news_sentiment: str = Field(..., description="新闻整体情感倾向")
    news_summary: str = Field(..., description="新闻摘要")

    # 综合分析
    overall_rating: str = Field(..., description="综合评级：买入/持有/卖出")
    risk_level: str = Field(..., description="风险等级：低/中/高")
    investment_advice: str = Field(..., description="投资建议")

    # AI生成的分析报告
    ai_analysis: str = Field(..., description="AI生成的详细分析报告")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DataSourceConfig(BaseModel):
    """数据源配置模型"""

    source_id: str = Field(..., description="数据源唯一标识")
    name: str = Field(..., description="数据源名称")
    source_type: str = Field(..., description="数据源类型：rss/api/web/social/custom")
    adapter_class: str = Field(..., description="适配器类名")
    config: Dict[str, Any] = Field(default_factory=dict, description="数据源特定配置")
    enabled: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=1, description="优先级，数字越小优先级越高")
    fetch_interval: int = Field(default=300, description="获取间隔（秒）")
    retry_count: int = Field(default=3, description="重试次数")
    timeout: int = Field(default=30, description="超时时间（秒）")
    created_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_time: Optional[datetime] = Field(default=None, description="更新时间")
    last_fetch_time: Optional[datetime] = Field(default=None, description="最后获取时间")
    last_success_time: Optional[datetime] = Field(default=None, description="最后成功时间")
    error_count: int = Field(default=0, description="错误次数")
    last_error: Optional[str] = Field(default=None, description="最后错误信息")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HotNewsChannel(BaseModel):
    """热点信息数据源渠道模型（保持向后兼容）"""

    channel_id: str = Field(..., description="渠道唯一标识")
    name: str = Field(..., description="渠道名称")
    channel_type: str = Field(..., description="渠道类型：rss/api/web/social")
    url: Optional[str] = Field(default=None, description="数据源URL")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    headers: Optional[Dict[str, str]] = Field(default=None, description="请求头")
    params: Optional[Dict[str, Any]] = Field(default=None, description="请求参数")
    enabled: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=1, description="优先级，数字越小优先级越高")
    fetch_interval: int = Field(default=300, description="获取间隔（秒）")
    last_fetch_time: Optional[datetime] = Field(default=None, description="最后获取时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HotNewsItem(BaseModel):
    """热点信息条目模型"""

    news_id: str = Field(..., description="新闻唯一标识")
    title: str = Field(..., description="新闻标题")
    content: Optional[str] = Field(default=None, description="新闻内容")
    summary: Optional[str] = Field(default=None, description="新闻摘要")
    source: str = Field(..., description="新闻来源")
    channel_id: str = Field(..., description="来源渠道ID")
    url: Optional[str] = Field(default=None, description="新闻链接")
    image_url: Optional[str] = Field(default=None, description="配图链接")
    publish_time: datetime = Field(..., description="发布时间")
    fetch_time: datetime = Field(..., description="获取时间")

    # 热度相关
    heat_score: Optional[float] = Field(default=None, description="热度评分")
    view_count: Optional[int] = Field(default=None, description="浏览量")
    comment_count: Optional[int] = Field(default=None, description="评论数")
    share_count: Optional[int] = Field(default=None, description="分享数")

    # 分类和标签
    category: Optional[str] = Field(default=None, description="新闻分类")
    tags: List[str] = Field(default_factory=list, description="新闻标签")
    keywords: List[str] = Field(default_factory=list, description="关键词")

    # AI分析结果
    is_historical: Optional[bool] = Field(default=None, description="是否为历史信息")
    sentiment: Optional[str] = Field(default=None, description="情感倾向：positive/negative/neutral")
    importance_level: Optional[str] = Field(default=None, description="重要程度：high/medium/low")

    # 推送状态
    is_pushed: bool = Field(default=False, description="是否已推送")
    push_time: Optional[datetime] = Field(default=None, description="推送时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UnifiedNewsData(BaseModel):
    """统一的新闻数据结构"""

    # 基础信息
    id: str = Field(..., description="新闻唯一标识")
    title: str = Field(..., description="新闻标题")
    content: Optional[str] = Field(default=None, description="新闻内容")
    summary: Optional[str] = Field(default=None, description="新闻摘要")

    # 来源信息
    source_id: str = Field(..., description="数据源ID")
    source_name: str = Field(..., description="数据源名称")
    original_url: Optional[str] = Field(default=None, description="原始链接")

    # 时间信息
    publish_time: Optional[datetime] = Field(default=None, description="发布时间")
    fetch_time: datetime = Field(default_factory=datetime.now, description="获取时间")

    # 媒体信息
    image_urls: List[str] = Field(default_factory=list, description="图片链接列表")
    video_urls: List[str] = Field(default_factory=list, description="视频链接列表")

    # 分类和标签
    category: Optional[str] = Field(default=None, description="新闻分类")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    keywords: List[str] = Field(default_factory=list, description="关键词列表")

    # 统计信息
    view_count: Optional[int] = Field(default=None, description="浏览量")
    comment_count: Optional[int] = Field(default=None, description="评论数")
    share_count: Optional[int] = Field(default=None, description="分享数")
    like_count: Optional[int] = Field(default=None, description="点赞数")

    # 扩展字段
    extra_data: Dict[str, Any] = Field(default_factory=dict, description="扩展数据")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DataSourceStatus(BaseModel):
    """数据源状态模型"""

    source_id: str = Field(..., description="数据源ID")
    status: str = Field(..., description="状态：active/inactive/error/disabled")
    last_check_time: datetime = Field(default_factory=datetime.now, description="最后检查时间")
    last_success_time: Optional[datetime] = Field(default=None, description="最后成功时间")
    error_count: int = Field(default=0, description="错误次数")
    success_count: int = Field(default=0, description="成功次数")
    total_fetched: int = Field(default=0, description="总获取数量")
    last_error: Optional[str] = Field(default=None, description="最后错误信息")
    response_time: Optional[float] = Field(default=None, description="响应时间（秒）")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UnifiedStockNews(BaseModel):
    """统一股票新闻模型"""

    # 基础信息
    id: str = Field(..., description="新闻唯一标识")
    title: str = Field(..., description="新闻标题")
    content: Optional[str] = Field(default=None, description="新闻内容")
    source: str = Field(..., description="新闻来源")
    source_type: str = Field(..., description="来源类型：search/hot_news")
    url: Optional[str] = Field(default=None, description="新闻链接")

    # 时间信息
    publish_time: datetime = Field(..., description="发布时间")

    # 股票关联信息
    stock_symbol: str = Field(..., description="关联股票代码")
    stock_name: str = Field(..., description="关联股票名称")

    # 相关性评估
    relevance_score: float = Field(default=50.0, description="相关性评分（0-100）")
    relevance_level: str = Field(default="medium", description="相关性级别：very_high/high/medium/low/very_low")

    # 分析结果
    sentiment: Optional[str] = Field(default=None, description="情感倾向：positive/negative/neutral")
    category: Optional[str] = Field(default=None, description="新闻分类")

    # 热点新闻特有字段
    importance_level: Optional[str] = Field(default=None, description="重要程度：high/medium/low")
    heat_score: Optional[float] = Field(default=None, description="热度评分")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HotNewsCache(BaseModel):
    """热点信息缓存模型"""

    cache_key: str = Field(..., description="缓存键")
    data: Any = Field(..., description="缓存数据")
    created_time: datetime = Field(..., description="创建时间")
    expire_time: datetime = Field(..., description="过期时间")
    access_count: int = Field(default=0, description="访问次数")
    last_access_time: Optional[datetime] = Field(default=None, description="最后访问时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# ==================== LLM 服务层数据模型 ====================

class LLMRequest(BaseModel):
    """统一的LLM请求模型"""

    # 基础参数
    prompt: str = Field(..., description="提示词，必填")
    system_instruction: Optional[str] = Field(default=None, description="系统指令")

    # 功能开关
    search: bool = Field(default=False, description="是否启用搜索工具")
    stream: bool = Field(default=False, description="是否启用流式响应")

    # 模型参数
    temperature: Optional[float] = Field(default=None, description="温度参数，控制随机性 (0.0-2.0)")
    max_tokens: Optional[int] = Field(default=None, description="最大生成token数")
    top_p: Optional[float] = Field(default=None, description="核采样参数 (0.0-1.0)")
    top_k: Optional[int] = Field(default=None, description="Top-K采样参数")
    frequency_penalty: Optional[float] = Field(default=None, description="频率惩罚 (-2.0-2.0)")
    presence_penalty: Optional[float] = Field(default=None, description="存在惩罚 (-2.0-2.0)")

    # 扩展参数
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="提供商特定的额外参数")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LLMUsage(BaseModel):
    """LLM使用统计模型"""

    prompt_tokens: Optional[int] = Field(default=None, description="输入token数")
    completion_tokens: Optional[int] = Field(default=None, description="输出token数")
    total_tokens: Optional[int] = Field(default=None, description="总token数")

    # 成本信息（可选）
    prompt_cost: Optional[float] = Field(default=None, description="输入成本")
    completion_cost: Optional[float] = Field(default=None, description="输出成本")
    total_cost: Optional[float] = Field(default=None, description="总成本")


class LLMResponse(BaseModel):
    """统一的LLM响应模型"""

    # 响应内容
    content: str = Field(..., description="生成的文本内容")

    # 元数据
    model: str = Field(..., description="使用的模型名称")
    provider: str = Field(..., description="LLM提供商")
    request_id: Optional[str] = Field(default=None, description="请求ID")

    # 使用统计
    usage: Optional[LLMUsage] = Field(default=None, description="使用统计")

    # 时间信息
    created_at: datetime = Field(default_factory=datetime.now, description="响应创建时间")
    response_time: Optional[float] = Field(default=None, description="响应时间（秒）")

    # 质量评估
    finish_reason: Optional[str] = Field(default=None, description="完成原因：stop/length/content_filter等")
    confidence_score: Optional[float] = Field(default=None, description="置信度评分 (0.0-1.0)")

    # 扩展信息
    extra_data: Dict[str, Any] = Field(default_factory=dict, description="提供商特定的额外数据")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LLMStreamChunk(BaseModel):
    """LLM流式响应块模型"""

    # 内容
    content: str = Field(..., description="当前块的文本内容")
    delta: str = Field(..., description="相对于上一块的增量内容")

    # 元数据
    chunk_id: int = Field(..., description="块序号")
    is_final: bool = Field(default=False, description="是否为最后一块")

    # 进度信息
    progress_percentage: Optional[float] = Field(default=None, description="进度百分比 (0.0-100.0)")
    estimated_total_chunks: Optional[int] = Field(default=None, description="预估总块数")

    # 流式控制
    is_cancelled: bool = Field(default=False, description="是否已取消")
    cancellation_token: Optional[str] = Field(default=None, description="取消令牌")

    # 提供商信息
    provider: Optional[str] = Field(default=None, description="LLM提供商")
    model: Optional[str] = Field(default=None, description="使用的模型")
    is_simulated_stream: bool = Field(default=False, description="是否为模拟流式响应")

    # 时间信息
    timestamp: datetime = Field(default_factory=datetime.now, description="块生成时间")
    chunk_duration: Optional[float] = Field(default=None, description="块生成耗时（秒）")

    # 质量信息
    confidence_score: Optional[float] = Field(default=None, description="当前块的置信度 (0.0-1.0)")

    # 错误处理
    has_error: bool = Field(default=False, description="是否包含错误")
    error_message: Optional[str] = Field(default=None, description="错误消息")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LLMError(BaseModel):
    """LLM错误信息模型"""

    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    error_type: str = Field(..., description="错误类型：api_error/rate_limit/timeout/validation等")

    # 详细信息
    provider: str = Field(..., description="出错的LLM提供商")
    model: Optional[str] = Field(default=None, description="出错的模型")
    request_id: Optional[str] = Field(default=None, description="请求ID")

    # 重试信息
    retry_after: Optional[int] = Field(default=None, description="建议重试间隔（秒）")
    is_retryable: bool = Field(default=False, description="是否可重试")

    # 时间信息
    occurred_at: datetime = Field(default_factory=datetime.now, description="错误发生时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LLMStreamConfig(BaseModel):
    """LLM流式响应配置模型"""

    # 基础配置
    enabled: bool = Field(default=True, description="是否启用流式响应")
    chunk_size: int = Field(default=50, description="流式块大小（字符数）")
    chunk_delay: float = Field(default=0.1, description="块之间的延迟（秒）")

    # 分割策略
    split_by_sentence: bool = Field(default=True, description="是否按句子分割")
    split_by_word: bool = Field(default=False, description="是否按单词分割")
    preserve_formatting: bool = Field(default=True, description="是否保持格式")

    # 控制配置
    max_chunks: Optional[int] = Field(default=None, description="最大块数限制")
    timeout: int = Field(default=300, description="流式响应超时时间（秒）")
    buffer_size: int = Field(default=1024, description="缓冲区大小")

    # 质量配置
    enable_progress: bool = Field(default=True, description="是否启用进度报告")
    enable_cancellation: bool = Field(default=True, description="是否支持取消")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LLMProviderConfig(BaseModel):
    """LLM提供商配置模型"""

    # 基础信息
    provider_id: str = Field(..., description="提供商唯一标识")
    provider_name: str = Field(..., description="提供商名称")
    provider_type: str = Field(..., description="提供商类型：windmill/openai/gemini/qwen等")

    # 连接配置
    base_url: Optional[str] = Field(default=None, description="API基础URL")
    api_key: Optional[str] = Field(default=None, description="API密钥")

    # 模型配置
    default_model: str = Field(..., description="默认模型名称")
    available_models: List[str] = Field(default_factory=list, description="可用模型列表")

    # 流式支持
    supports_streaming: bool = Field(default=False, description="是否支持原生流式响应")
    stream_config: LLMStreamConfig = Field(default_factory=LLMStreamConfig, description="流式响应配置")

    # 限制配置
    max_tokens_limit: Optional[int] = Field(default=None, description="最大token限制")
    rate_limit_rpm: Optional[int] = Field(default=None, description="每分钟请求限制")
    rate_limit_tpm: Optional[int] = Field(default=None, description="每分钟token限制")

    # 重试配置
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    timeout: int = Field(default=60, description="请求超时时间（秒）")

    # 状态信息
    enabled: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=1, description="优先级，数字越小优先级越高")

    # 扩展配置
    extra_config: Dict[str, Any] = Field(default_factory=dict, description="提供商特定的额外配置")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StreamCancellationToken(BaseModel):
    """流式响应取消令牌模型"""

    token_id: str = Field(..., description="取消令牌唯一标识")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    is_cancelled: bool = Field(default=False, description="是否已取消")
    cancelled_at: Optional[datetime] = Field(default=None, description="取消时间")
    reason: Optional[str] = Field(default=None, description="取消原因")

    def cancel(self, reason: str = "用户取消"):
        """取消流式响应"""
        self.is_cancelled = True
        self.cancelled_at = datetime.now()
        self.reason = reason

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class StreamProgress(BaseModel):
    """流式响应进度模型"""

    current_chunk: int = Field(..., description="当前块序号")
    total_chunks: Optional[int] = Field(default=None, description="总块数")
    percentage: Optional[float] = Field(default=None, description="进度百分比")

    # 内容统计
    current_length: int = Field(default=0, description="当前内容长度")
    estimated_total_length: Optional[int] = Field(default=None, description="预估总长度")

    # 时间统计
    start_time: datetime = Field(default_factory=datetime.now, description="开始时间")
    current_time: datetime = Field(default_factory=datetime.now, description="当前时间")
    estimated_completion_time: Optional[datetime] = Field(default=None, description="预估完成时间")

    # 速度统计
    chunks_per_second: Optional[float] = Field(default=None, description="每秒块数")
    characters_per_second: Optional[float] = Field(default=None, description="每秒字符数")

    def update_progress(self, chunk_id: int, content_length: int, total_chunks: Optional[int] = None):
        """更新进度信息"""
        self.current_chunk = chunk_id
        self.current_length = content_length
        self.current_time = datetime.now()

        if total_chunks:
            self.total_chunks = total_chunks
            self.percentage = (chunk_id / total_chunks) * 100

        # 计算速度
        elapsed = (self.current_time - self.start_time).total_seconds()
        if elapsed > 0:
            self.chunks_per_second = chunk_id / elapsed
            self.characters_per_second = content_length / elapsed

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
