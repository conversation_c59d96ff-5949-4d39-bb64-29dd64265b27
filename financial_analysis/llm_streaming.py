"""
LLM流式响应处理模块

提供智能流式降级机制，为不支持原生流式的提供商实现模拟流式输出。
包括文本分割、进度控制、取消机制等功能。
"""

import asyncio
import re
import time
import uuid
from typing import AsyncGenerator, Optional, List, Callable, Any
from collections import deque
from loguru import logger

from .models import (
    LLMResponse, LLMStreamChunk, LLMStreamConfig, 
    StreamCancellationToken, StreamProgress
)


class TextSplitter:
    """文本分割器
    
    根据配置将完整文本分割成适合流式输出的块。
    支持按句子、单词、字符等多种分割策略。
    """
    
    def __init__(self, config: LLMStreamConfig):
        """
        初始化文本分割器
        
        Args:
            config: 流式配置
        """
        self.config = config
        
        # 句子分割正则表达式（支持中英文）
        self.sentence_pattern = re.compile(
            r'[.!?。！？；;]\s*|[\n\r]+',
            re.MULTILINE
        )
        
        # 单词分割正则表达式
        self.word_pattern = re.compile(r'\s+')
        
        logger.debug(f"文本分割器初始化完成，配置: {config}")
    
    def split_text(self, text: str) -> List[str]:
        """
        分割文本为块
        
        Args:
            text: 要分割的文本
            
        Returns:
            文本块列表
        """
        if not text:
            return []
        
        chunks = []
        
        if self.config.split_by_sentence:
            chunks = self._split_by_sentence(text)
        elif self.config.split_by_word:
            chunks = self._split_by_word(text)
        else:
            chunks = self._split_by_character(text)
        
        # 过滤空块
        chunks = [chunk for chunk in chunks if chunk.strip()]
        
        logger.debug(f"文本分割完成，原长度: {len(text)}, 块数: {len(chunks)}")
        return chunks
    
    def _split_by_sentence(self, text: str) -> List[str]:
        """按句子分割文本"""
        # 简单按字符数分割，保持原文本完整性
        if len(text) <= self.config.chunk_size:
            return [text]

        chunks = []
        current_pos = 0

        while current_pos < len(text):
            # 计算当前块的结束位置
            end_pos = min(current_pos + self.config.chunk_size, len(text))

            # 如果不是最后一块，尝试在句子边界分割
            if end_pos < len(text):
                # 向后查找句子结束符
                for i in range(end_pos, current_pos, -1):
                    if text[i-1] in '。！？.!?；;':
                        end_pos = i
                        break
                else:
                    # 如果没找到句子边界，在空格处分割
                    for i in range(end_pos, current_pos, -1):
                        if text[i-1] == ' ':
                            end_pos = i
                            break

            # 提取当前块
            chunk = text[current_pos:end_pos].strip()
            if chunk:
                chunks.append(chunk)

            current_pos = end_pos

        return chunks
    
    def _split_by_word(self, text: str) -> List[str]:
        """按单词分割文本"""
        words = self.word_pattern.split(text)
        chunks = []
        current_chunk = ""
        
        for word in words:
            word = word.strip()
            if not word:
                continue
            
            # 检查是否超过块大小限制
            test_chunk = f"{current_chunk} {word}" if current_chunk else word
            if len(test_chunk) > self.config.chunk_size and current_chunk:
                chunks.append(current_chunk)
                current_chunk = word
            else:
                current_chunk = test_chunk
        
        # 添加最后一块
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _split_by_character(self, text: str) -> List[str]:
        """按字符数分割文本"""
        chunks = []
        chunk_size = self.config.chunk_size
        
        for i in range(0, len(text), chunk_size):
            chunk = text[i:i + chunk_size]
            chunks.append(chunk)
        
        return chunks


class StreamSimulator:
    """流式响应模拟器
    
    将完整的LLM响应转换为流式输出，支持进度控制、取消机制等。
    """
    
    def __init__(self, config: LLMStreamConfig):
        """
        初始化流式模拟器
        
        Args:
            config: 流式配置
        """
        self.config = config
        self.text_splitter = TextSplitter(config)
        logger.debug("流式响应模拟器初始化完成")
    
    async def simulate_stream(self, response: LLMResponse, 
                            cancellation_token: Optional[StreamCancellationToken] = None,
                            progress_callback: Optional[Callable[[StreamProgress], None]] = None
                            ) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        模拟流式响应
        
        Args:
            response: 完整的LLM响应
            cancellation_token: 取消令牌
            progress_callback: 进度回调函数
            
        Yields:
            流式响应块
        """
        try:
            # 分割文本
            text_chunks = self.text_splitter.split_text(response.content)
            total_chunks = len(text_chunks)
            
            if total_chunks == 0:
                logger.warning("文本分割后没有有效块")
                return
            
            # 初始化进度跟踪
            progress = StreamProgress(
                current_chunk=0,
                total_chunks=total_chunks
            )
            
            accumulated_content = ""
            start_time = time.time()
            
            logger.info(f"开始模拟流式输出，总块数: {total_chunks}")
            
            for i, chunk_text in enumerate(text_chunks):
                # 在每个块之前检查取消状态
                if cancellation_token and cancellation_token.is_cancelled:
                    logger.info(f"流式响应已取消: {cancellation_token.reason}")

                    # 发送取消块
                    cancel_chunk = LLMStreamChunk(
                        content=accumulated_content,
                        delta="",
                        chunk_id=i,
                        is_final=True,
                        is_cancelled=True,
                        cancellation_token=cancellation_token.token_id,
                        provider=response.provider,
                        model=response.model,
                        is_simulated_stream=True,
                        progress_percentage=progress.percentage,
                        estimated_total_chunks=total_chunks
                    )
                    yield cancel_chunk
                    return
                
                # 累积内容
                accumulated_content += chunk_text
                is_final = (i == total_chunks - 1)
                
                # 计算进度
                progress.update_progress(i + 1, len(accumulated_content), total_chunks)
                
                # 创建流式块
                chunk = LLMStreamChunk(
                    content=accumulated_content,
                    delta=chunk_text,
                    chunk_id=i,
                    is_final=is_final,
                    progress_percentage=progress.percentage,
                    estimated_total_chunks=total_chunks,
                    provider=response.provider,
                    model=response.model,
                    is_simulated_stream=True,
                    chunk_duration=time.time() - start_time if i == 0 else self.config.chunk_delay
                )
                
                # 调用进度回调
                if progress_callback:
                    try:
                        progress_callback(progress)
                    except Exception as e:
                        logger.warning(f"进度回调执行失败: {str(e)}")
                
                yield chunk

                # 如果不是最后一块，添加延迟并再次检查取消状态
                if not is_final:
                    await asyncio.sleep(self.config.chunk_delay)

                    # 在延迟后再次检查取消状态
                    if cancellation_token and cancellation_token.is_cancelled:
                        logger.info(f"流式响应在延迟后被取消: {cancellation_token.reason}")

                        # 发送取消块
                        cancel_chunk = LLMStreamChunk(
                            content=accumulated_content,
                            delta="",
                            chunk_id=i + 1,
                            is_final=True,
                            is_cancelled=True,
                            cancellation_token=cancellation_token.token_id,
                            provider=response.provider,
                            model=response.model,
                            is_simulated_stream=True,
                            progress_percentage=progress.percentage,
                            estimated_total_chunks=total_chunks
                        )
                        yield cancel_chunk
                        return
            
            total_time = time.time() - start_time
            logger.info(f"流式输出完成，总耗时: {total_time:.2f}秒，平均每块: {total_time/total_chunks:.3f}秒")
            
        except Exception as e:
            logger.error(f"流式响应模拟失败: {str(e)}")
            
            # 发送错误块
            error_chunk = LLMStreamChunk(
                content=accumulated_content if 'accumulated_content' in locals() else "",
                delta="",
                chunk_id=i if 'i' in locals() else 0,
                is_final=True,
                has_error=True,
                error_message=str(e),
                provider=response.provider,
                model=response.model,
                is_simulated_stream=True
            )
            yield error_chunk


class StreamingManager:
    """流式响应管理器
    
    管理流式响应的生命周期，包括取消令牌、进度跟踪等。
    """
    
    def __init__(self):
        """初始化流式响应管理器"""
        self.active_streams: dict[str, StreamCancellationToken] = {}
        logger.debug("流式响应管理器初始化完成")
    
    def create_cancellation_token(self) -> StreamCancellationToken:
        """
        创建取消令牌
        
        Returns:
            取消令牌
        """
        token = StreamCancellationToken(token_id=str(uuid.uuid4()))
        self.active_streams[token.token_id] = token
        
        logger.debug(f"创建取消令牌: {token.token_id}")
        return token
    
    def cancel_stream(self, token_id: str, reason: str = "用户取消") -> bool:
        """
        取消流式响应
        
        Args:
            token_id: 取消令牌ID
            reason: 取消原因
            
        Returns:
            是否成功取消
        """
        if token_id in self.active_streams:
            token = self.active_streams[token_id]
            token.cancel(reason)
            logger.info(f"流式响应已取消: {token_id}, 原因: {reason}")
            return True
        else:
            logger.warning(f"未找到取消令牌: {token_id}")
            return False
    
    def cleanup_token(self, token_id: str):
        """
        清理取消令牌
        
        Args:
            token_id: 取消令牌ID
        """
        if token_id in self.active_streams:
            del self.active_streams[token_id]
            logger.debug(f"清理取消令牌: {token_id}")
    
    def get_active_streams(self) -> List[str]:
        """
        获取活跃的流式响应列表
        
        Returns:
            活跃流式响应的令牌ID列表
        """
        return list(self.active_streams.keys())
    
    def get_stream_status(self, token_id: str) -> Optional[dict]:
        """
        获取流式响应状态
        
        Args:
            token_id: 取消令牌ID
            
        Returns:
            流式响应状态信息
        """
        if token_id in self.active_streams:
            token = self.active_streams[token_id]
            return {
                "token_id": token.token_id,
                "created_at": token.created_at,
                "is_cancelled": token.is_cancelled,
                "cancelled_at": token.cancelled_at,
                "reason": token.reason
            }
        return None


class StreamBuffer:
    """流式响应缓冲区

    管理流式响应的缓冲，支持背压控制和内存管理。
    """

    def __init__(self, max_size: int = 1024):
        """
        初始化流式缓冲区

        Args:
            max_size: 最大缓冲区大小
        """
        self.max_size = max_size
        self.buffer = deque()
        self.current_size = 0
        self._lock = asyncio.Lock()

        logger.debug(f"流式缓冲区初始化，最大大小: {max_size}")

    async def put(self, chunk: LLMStreamChunk) -> bool:
        """
        添加块到缓冲区

        Args:
            chunk: 流式响应块

        Returns:
            是否成功添加
        """
        async with self._lock:
            chunk_size = len(chunk.delta)

            # 检查缓冲区是否已满
            if self.current_size + chunk_size > self.max_size:
                logger.warning(f"流式缓冲区已满，当前大小: {self.current_size}, 块大小: {chunk_size}")
                return False

            self.buffer.append(chunk)
            self.current_size += chunk_size
            return True

    async def get(self) -> Optional[LLMStreamChunk]:
        """
        从缓冲区获取块

        Returns:
            流式响应块，如果缓冲区为空返回None
        """
        async with self._lock:
            if not self.buffer:
                return None

            chunk = self.buffer.popleft()
            self.current_size -= len(chunk.delta)
            return chunk

    async def clear(self):
        """清空缓冲区"""
        async with self._lock:
            self.buffer.clear()
            self.current_size = 0
            logger.debug("流式缓冲区已清空")

    def is_empty(self) -> bool:
        """检查缓冲区是否为空"""
        return len(self.buffer) == 0

    def is_full(self) -> bool:
        """检查缓冲区是否已满"""
        return self.current_size >= self.max_size


class StreamTimeoutManager:
    """流式响应超时管理器

    管理流式响应的超时控制，防止长时间阻塞。
    """

    def __init__(self, default_timeout: int = 300):
        """
        初始化超时管理器

        Args:
            default_timeout: 默认超时时间（秒）
        """
        self.default_timeout = default_timeout
        self.active_timeouts: Dict[str, asyncio.Task] = {}

        logger.debug(f"流式超时管理器初始化，默认超时: {default_timeout}秒")

    async def start_timeout(self, token_id: str, timeout: Optional[int] = None) -> bool:
        """
        开始超时计时

        Args:
            token_id: 取消令牌ID
            timeout: 超时时间（秒），为None时使用默认值

        Returns:
            是否成功开始计时
        """
        timeout = timeout or self.default_timeout

        if token_id in self.active_timeouts:
            logger.warning(f"令牌 {token_id} 的超时计时已存在")
            return False

        # 创建超时任务
        timeout_task = asyncio.create_task(self._timeout_handler(token_id, timeout))
        self.active_timeouts[token_id] = timeout_task

        logger.debug(f"开始流式响应超时计时: {token_id}, 超时时间: {timeout}秒")
        return True

    async def cancel_timeout(self, token_id: str) -> bool:
        """
        取消超时计时

        Args:
            token_id: 取消令牌ID

        Returns:
            是否成功取消
        """
        if token_id not in self.active_timeouts:
            return False

        timeout_task = self.active_timeouts[token_id]
        timeout_task.cancel()
        del self.active_timeouts[token_id]

        logger.debug(f"取消流式响应超时计时: {token_id}")
        return True

    async def _timeout_handler(self, token_id: str, timeout: int):
        """
        超时处理器

        Args:
            token_id: 取消令牌ID
            timeout: 超时时间
        """
        try:
            await asyncio.sleep(timeout)

            # 超时后自动取消流式响应
            logger.warning(f"流式响应超时，自动取消: {token_id}")
            streaming_manager.cancel_stream(token_id, f"超时({timeout}秒)")

        except asyncio.CancelledError:
            logger.debug(f"超时计时被取消: {token_id}")
        finally:
            # 清理超时任务
            if token_id in self.active_timeouts:
                del self.active_timeouts[token_id]


class EnhancedStreamSimulator(StreamSimulator):
    """增强的流式响应模拟器

    在基础模拟器基础上添加缓冲区管理、超时控制等功能。
    """

    def __init__(self, config: LLMStreamConfig):
        """
        初始化增强流式模拟器

        Args:
            config: 流式配置
        """
        super().__init__(config)
        self.buffer = StreamBuffer(config.buffer_size)
        self.timeout_manager = StreamTimeoutManager(config.timeout)

        logger.debug("增强流式响应模拟器初始化完成")

    async def simulate_stream_with_control(self, response: LLMResponse,
                                         cancellation_token: Optional[StreamCancellationToken] = None,
                                         progress_callback: Optional[Callable[[StreamProgress], None]] = None,
                                         timeout: Optional[int] = None
                                         ) -> AsyncGenerator[LLMStreamChunk, None]:
        """
        带控制机制的流式响应模拟

        Args:
            response: 完整的LLM响应
            cancellation_token: 取消令牌
            progress_callback: 进度回调函数
            timeout: 超时时间

        Yields:
            流式响应块
        """
        token_id = cancellation_token.token_id if cancellation_token else str(uuid.uuid4())

        try:
            # 开始超时计时
            if timeout and cancellation_token:
                await self.timeout_manager.start_timeout(token_id, timeout)

            # 使用基础模拟器生成流式响应
            async for chunk in self.simulate_stream(response, cancellation_token, progress_callback):
                # 检查缓冲区
                if not await self.buffer.put(chunk):
                    logger.warning("流式缓冲区已满，跳过当前块")
                    continue

                # 从缓冲区获取并发送
                buffered_chunk = await self.buffer.get()
                if buffered_chunk:
                    yield buffered_chunk

                # 如果是最后一块，停止处理
                if chunk.is_final:
                    break

        except Exception as e:
            logger.error(f"增强流式响应模拟失败: {str(e)}")
            raise
        finally:
            # 清理资源
            await self.buffer.clear()
            if cancellation_token:
                await self.timeout_manager.cancel_timeout(token_id)


# 全局流式响应管理器实例
streaming_manager = StreamingManager()
