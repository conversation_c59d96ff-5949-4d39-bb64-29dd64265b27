"""
LLM服务限流和降级模块

实现请求限流、自动重试和服务降级策略，提高系统稳定性和可靠性。
支持基于令牌桶算法的限流、指数退避重试和智能降级。
"""

import asyncio
import time
from collections import defaultdict, deque
from typing import Dict, Optional, Any, Callable, Awaitable
from dataclasses import dataclass
from loguru import logger

from .llm_service import LLMServiceError, RateLimitError


@dataclass
class RateLimitConfig:
    """限流配置"""
    requests_per_minute: int = 60  # 每分钟请求数限制
    tokens_per_minute: int = 100000  # 每分钟token数限制
    burst_size: int = 10  # 突发请求大小
    window_size: int = 60  # 时间窗口大小（秒）


class TokenBucket:
    """令牌桶限流器
    
    实现基于令牌桶算法的限流机制，支持突发请求和平滑限流。
    """
    
    def __init__(self, capacity: int, refill_rate: float):
        """
        初始化令牌桶
        
        Args:
            capacity: 桶容量（最大令牌数）
            refill_rate: 令牌补充速率（每秒补充的令牌数）
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = capacity
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """
        获取令牌
        
        Args:
            tokens: 需要的令牌数
            
        Returns:
            是否成功获取令牌
        """
        async with self._lock:
            now = time.time()
            
            # 补充令牌
            time_passed = now - self.last_refill
            new_tokens = time_passed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + new_tokens)
            self.last_refill = now
            
            # 检查是否有足够的令牌
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            else:
                return False
    
    async def wait_for_tokens(self, tokens: int = 1, timeout: float = 60.0) -> bool:
        """
        等待获取令牌
        
        Args:
            tokens: 需要的令牌数
            timeout: 超时时间（秒）
            
        Returns:
            是否成功获取令牌
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if await self.acquire(tokens):
                return True
            
            # 计算等待时间
            wait_time = min(1.0, tokens / self.refill_rate)
            await asyncio.sleep(wait_time)
        
        return False


class SlidingWindowRateLimiter:
    """滑动窗口限流器
    
    基于滑动窗口算法实现精确的限流控制。
    """
    
    def __init__(self, config: RateLimitConfig):
        """
        初始化滑动窗口限流器
        
        Args:
            config: 限流配置
        """
        self.config = config
        self.request_times = deque()
        self.token_usage = deque()
        self._lock = asyncio.Lock()
    
    async def can_proceed(self, estimated_tokens: int = 1000) -> bool:
        """
        检查是否可以继续处理请求
        
        Args:
            estimated_tokens: 估算的token使用量
            
        Returns:
            是否可以继续处理
        """
        async with self._lock:
            now = time.time()
            window_start = now - self.config.window_size
            
            # 清理过期记录
            while self.request_times and self.request_times[0] < window_start:
                self.request_times.popleft()
            
            while self.token_usage and self.token_usage[0][0] < window_start:
                self.token_usage.popleft()
            
            # 检查请求数限制
            if len(self.request_times) >= self.config.requests_per_minute:
                return False
            
            # 检查token使用量限制
            total_tokens = sum(usage[1] for usage in self.token_usage)
            if total_tokens + estimated_tokens > self.config.tokens_per_minute:
                return False
            
            return True
    
    async def record_request(self, actual_tokens: int = 1000):
        """
        记录请求
        
        Args:
            actual_tokens: 实际使用的token数
        """
        async with self._lock:
            now = time.time()
            self.request_times.append(now)
            self.token_usage.append((now, actual_tokens))


class LLMRateLimiter:
    """LLM服务限流管理器
    
    为不同的LLM提供商提供独立的限流控制。
    """
    
    def __init__(self):
        """初始化LLM限流管理器"""
        self.provider_limiters: Dict[str, SlidingWindowRateLimiter] = {}
        self.provider_configs: Dict[str, RateLimitConfig] = {}
        logger.info("LLM限流管理器初始化完成")
    
    def configure_provider(self, provider_id: str, config: RateLimitConfig):
        """
        配置提供商限流参数
        
        Args:
            provider_id: 提供商ID
            config: 限流配置
        """
        self.provider_configs[provider_id] = config
        self.provider_limiters[provider_id] = SlidingWindowRateLimiter(config)
        logger.info(f"配置提供商 {provider_id} 限流参数: {config.requests_per_minute} req/min, {config.tokens_per_minute} tokens/min")
    
    async def check_rate_limit(self, provider_id: str, estimated_tokens: int = 1000) -> bool:
        """
        检查速率限制
        
        Args:
            provider_id: 提供商ID
            estimated_tokens: 估算的token使用量
            
        Returns:
            是否通过限流检查
        """
        if provider_id not in self.provider_limiters:
            # 如果没有配置限流，使用默认配置
            default_config = RateLimitConfig()
            self.configure_provider(provider_id, default_config)
        
        limiter = self.provider_limiters[provider_id]
        return await limiter.can_proceed(estimated_tokens)
    
    async def record_usage(self, provider_id: str, actual_tokens: int):
        """
        记录实际使用量
        
        Args:
            provider_id: 提供商ID
            actual_tokens: 实际使用的token数
        """
        if provider_id in self.provider_limiters:
            limiter = self.provider_limiters[provider_id]
            await limiter.record_request(actual_tokens)


class CircuitBreaker:
    """熔断器
    
    实现服务熔断机制，在服务异常时自动切断请求，避免雪崩效应。
    """
    
    def __init__(self, failure_threshold: int = 5, 
                 recovery_timeout: int = 60,
                 success_threshold: int = 3):
        """
        初始化熔断器
        
        Args:
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间（秒）
            success_threshold: 成功阈值（半开状态下需要的连续成功次数）
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable[[], Awaitable[Any]]) -> Any:
        """
        通过熔断器调用函数
        
        Args:
            func: 要调用的异步函数
            
        Returns:
            函数执行结果
            
        Raises:
            LLMServiceError: 熔断器开启时抛出异常
        """
        async with self._lock:
            # 检查熔断器状态
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = "HALF_OPEN"
                    self.success_count = 0
                    logger.info("熔断器进入半开状态")
                else:
                    raise LLMServiceError(
                        "服务熔断中，请稍后重试",
                        "CIRCUIT_BREAKER_OPEN",
                        "circuit_breaker",
                        True
                    )
        
        try:
            result = await func()
            
            async with self._lock:
                if self.state == "HALF_OPEN":
                    self.success_count += 1
                    if self.success_count >= self.success_threshold:
                        self.state = "CLOSED"
                        self.failure_count = 0
                        logger.info("熔断器恢复到关闭状态")
                elif self.state == "CLOSED":
                    self.failure_count = 0
            
            return result
            
        except Exception as e:
            async with self._lock:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = "OPEN"
                    logger.warning(f"熔断器开启，失败次数: {self.failure_count}")
                elif self.state == "HALF_OPEN":
                    self.state = "OPEN"
                    logger.warning("半开状态下请求失败，熔断器重新开启")
            
            raise e
    
    def get_state(self) -> Dict[str, Any]:
        """
        获取熔断器状态
        
        Returns:
            状态信息字典
        """
        return {
            "state": self.state,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time
        }


class LLMServiceDegrader:
    """LLM服务降级器
    
    在服务异常时提供降级策略，确保系统基本功能可用。
    """
    
    def __init__(self):
        """初始化服务降级器"""
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.degraded_providers: set = set()
        logger.info("LLM服务降级器初始化完成")
    
    def get_circuit_breaker(self, provider_id: str) -> CircuitBreaker:
        """
        获取提供商的熔断器
        
        Args:
            provider_id: 提供商ID
            
        Returns:
            熔断器实例
        """
        if provider_id not in self.circuit_breakers:
            self.circuit_breakers[provider_id] = CircuitBreaker()
        
        return self.circuit_breakers[provider_id]
    
    async def call_with_degradation(self, provider_id: str, 
                                  func: Callable[[], Awaitable[Any]],
                                  fallback_func: Optional[Callable[[], Awaitable[Any]]] = None) -> Any:
        """
        带降级的服务调用
        
        Args:
            provider_id: 提供商ID
            func: 主要服务函数
            fallback_func: 降级服务函数
            
        Returns:
            服务调用结果
        """
        circuit_breaker = self.get_circuit_breaker(provider_id)
        
        try:
            return await circuit_breaker.call(func)
        except Exception as e:
            logger.warning(f"提供商 {provider_id} 服务异常: {str(e)}")
            
            if fallback_func:
                logger.info(f"使用降级服务处理请求: {provider_id}")
                self.degraded_providers.add(provider_id)
                return await fallback_func()
            else:
                raise e
    
    def get_degradation_status(self) -> Dict[str, Any]:
        """
        获取降级状态
        
        Returns:
            降级状态信息
        """
        return {
            "degraded_providers": list(self.degraded_providers),
            "circuit_breaker_states": {
                provider_id: breaker.get_state()
                for provider_id, breaker in self.circuit_breakers.items()
            }
        }


# 全局实例
llm_rate_limiter = LLMRateLimiter()
llm_degrader = LLMServiceDegrader()
