#!/usr/bin/env python3
"""
Crawl4AI 股票新闻系统测试脚本

测试 Crawl4AI 集成的各个组件功能
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import StockInfo
from financial_analysis.crawl4ai_news_crawler import (
    Crawl4aiNewsClient,
    StockNewsCrawler,
    NewsValidator,
    StockNewsCrawlAnalyzer,
    CrawledNewsItem
)


async def test_crawl4ai_client():
    """测试 Crawl4AI 客户端"""
    print("🧪 测试 Crawl4AI 客户端")
    
    try:
        async with Crawl4aiNewsClient() as client:
            # 健康检查
            is_healthy = await client.health_check()
            assert is_healthy, "Crawl4AI 服务不健康"
            print("✅ 健康检查通过")
            
            # 测试简单爬取
            result = await client.crawl_url("https://httpbin.org/html")
            assert result is not None, "爬取结果为空"
            assert result.get("success", False), "爬取失败"
            print("✅ 基础爬取功能正常")
            
            return True
            
    except Exception as e:
        print(f"❌ Crawl4AI 客户端测试失败: {str(e)}")
        return False


async def test_news_crawler():
    """测试新闻爬取器"""
    print("\n🧪 测试新闻爬取器")
    
    try:
        crawler = StockNewsCrawler()
        
        # 检查新闻源配置
        sources = crawler.news_sources
        assert len(sources) > 0, "没有配置新闻源"
        print(f"✅ 新闻源配置: {len(sources)} 个源")
        
        # 测试股票信息
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        
        # 模拟爬取（使用较小的数量）
        news_items = await crawler.crawl_stock_news(
            stock_info=stock_info,
            max_news_per_source=1,
            sources=["sina_finance"]  # 只测试一个源
        )
        
        print(f"✅ 爬取测试完成: {len(news_items)} 条新闻")
        
        # 验证新闻数据结构
        if news_items:
            news = news_items[0]
            assert hasattr(news, 'title'), "新闻缺少标题"
            assert hasattr(news, 'source'), "新闻缺少来源"
            assert hasattr(news, 'url'), "新闻缺少链接"
            print("✅ 新闻数据结构验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 新闻爬取器测试失败: {str(e)}")
        return False


async def test_news_validator():
    """测试新闻验证器"""
    print("\n🧪 测试新闻验证器")
    
    try:
        validator = NewsValidator()
        
        # 创建测试新闻
        test_news = [
            CrawledNewsItem(
                title="平安银行发布2024年第三季度财报",
                content="平安银行今日发布第三季度财报，净利润同比增长5%",
                url="https://example.com/news1",
                source="测试源",
                publish_time=datetime.now() - timedelta(hours=2)
            ),
            CrawledNewsItem(
                title="银行业监管政策调整",
                content="央行发布新的银行业监管政策",
                url="https://example.com/news2", 
                source="测试源",
                publish_time=datetime.now() - timedelta(days=2)
            )
        ]
        
        stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
        
        # 测试验证功能
        analyzed_news = await validator.validate_and_analyze_news(test_news, stock_info)
        
        print(f"✅ 验证测试完成: {len(analyzed_news)} 条分析结果")
        
        # 验证分析结果
        if analyzed_news:
            news = analyzed_news[0]
            assert hasattr(news, 'is_fresh'), "缺少新鲜度判断"
            assert hasattr(news, 'value_score'), "缺少价值评分"
            assert hasattr(news, 'sentiment'), "缺少情感分析"
            print("✅ 分析结果结构验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 新闻验证器测试失败: {str(e)}")
        return False


async def test_complete_analyzer():
    """测试完整分析器"""
    print("\n🧪 测试完整分析器")
    
    try:
        analyzer = StockNewsCrawlAnalyzer()
        
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE",
            sector="金融"
        )
        
        # 测试完整流程（使用较小的参数）
        analyzed_news = await analyzer.get_analyzed_stock_news(
            stock_info=stock_info,
            max_news_per_source=1,
            sources=["sina_finance"],
            min_value_score=0.0  # 降低阈值以便测试
        )
        
        print(f"✅ 完整分析测试: {len(analyzed_news)} 条结果")
        
        # 测试报告生成
        if analyzed_news:
            report = await analyzer.generate_news_report(stock_info, analyzed_news)
            assert isinstance(report, str), "报告应该是字符串"
            assert len(report) > 0, "报告不能为空"
            print("✅ 报告生成功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整分析器测试失败: {str(e)}")
        return False


def test_data_models():
    """测试数据模型"""
    print("\n🧪 测试数据模型")
    
    try:
        # 测试 CrawledNewsItem
        news_item = CrawledNewsItem(
            title="测试新闻",
            content="测试内容",
            url="https://example.com",
            source="测试源"
        )
        
        assert news_item.title == "测试新闻"
        assert news_item.is_fresh is None  # 初始值
        print("✅ CrawledNewsItem 模型正常")
        
        # 测试时间解析
        crawler = StockNewsCrawler()
        
        # 测试各种时间格式
        time_formats = [
            "2024-01-01 12:00:00",
            "2024-01-01 12:00",
            "2024-01-01",
            "2小时前",
            "30分钟前",
            "1天前"
        ]
        
        for time_str in time_formats:
            parsed_time = crawler._parse_time(time_str)
            # 某些格式可能解析失败，这是正常的
            print(f"   时间解析 '{time_str}': {'成功' if parsed_time else '失败'}")
        
        print("✅ 时间解析功能测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {str(e)}")
        return False


def test_configuration():
    """测试配置"""
    print("\n🧪 测试配置")
    
    try:
        crawler = StockNewsCrawler()
        
        # 检查新闻源配置
        sources = crawler.news_sources
        required_fields = ["name", "search_url_template", "extraction_config"]
        
        for source_key, source_config in sources.items():
            for field in required_fields:
                assert field in source_config, f"源 {source_key} 缺少字段 {field}"
            
            # 检查提取配置
            extraction_config = source_config["extraction_config"]
            assert "type" in extraction_config, f"源 {source_key} 缺少提取类型"
            assert "params" in extraction_config, f"源 {source_key} 缺少提取参数"
        
        print(f"✅ 新闻源配置验证通过: {len(sources)} 个源")
        
        # 检查URL模板
        for source_key, source_config in sources.items():
            template = source_config["search_url_template"]
            assert "{stock_name}" in template, f"源 {source_key} URL模板缺少股票名称占位符"
        
        print("✅ URL模板验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Crawl4AI 股票新闻系统测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_data_models())
    test_results.append(test_configuration())
    
    # 需要网络连接的测试
    print("\n⚠️  以下测试需要 Crawl4AI 服务运行在 localhost:11235")
    
    try:
        test_results.append(await test_crawl4ai_client())
        test_results.append(await test_news_crawler())
        test_results.append(await test_news_validator())
        test_results.append(await test_complete_analyzer())
    except Exception as e:
        print(f"❌ 网络测试失败: {str(e)}")
        test_results.extend([False, False, False, False])
    
    # 汇总结果
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果汇总:")
    print(f"   - 通过测试: {passed_tests}/{total_tests}")
    print(f"   - 成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        
        # 提供故障排除建议
        print("\n🔧 故障排除建议:")
        print("1. 确保 Crawl4AI 服务运行: docker run -d -p 11235:11235 unclecode/crawl4ai:latest")
        print("2. 检查网络连接和防火墙设置")
        print("3. 验证 Windmill 配置和API密钥")
        print("4. 查看详细错误日志")
        
        return 1


if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
