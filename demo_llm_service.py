#!/usr/bin/env python3
"""
LLM服务层演示脚本

展示统一LLM服务层的各种功能，包括：
1. 基础文本生成
2. 兼容性接口使用
3. 健康检查和监控
4. 错误处理
5. 实际应用场景演示
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import unified_llm_service, streaming_manager
from financial_analysis.llm_compatibility import (
    enhanced_windmill_client,
    get_llm_service_status,
    test_llm_service
)
from financial_analysis.llm_service import LLMServiceError, RateLimitError, ValidationError
from financial_analysis.models import LLMStreamChunk


def print_section(title: str):
    """打印章节标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_subsection(title: str):
    """打印子章节标题"""
    print(f"\n--- {title} ---")


async def demo_service_status():
    """演示服务状态检查"""
    print_section("LLM服务状态检查")
    
    # 获取服务状态
    print_subsection("服务状态信息")
    status = get_llm_service_status()
    print(f"服务可用性: {status['service_available']}")
    
    if status['service_available']:
        print(f"可用提供商: {status['providers']}")
        print(f"初始化状态: {status['initialization_status']}")
    else:
        print(f"服务错误: {status.get('error', '未知错误')}")
    
    # 运行服务测试
    print_subsection("服务功能测试")
    test_results = await test_llm_service()
    
    for test_name, result in test_results.items():
        if isinstance(result, dict):
            print(f"{test_name}: {result}")
        else:
            print(f"{test_name}: {result}")


async def demo_basic_usage():
    """演示基础使用功能"""
    print_section("基础功能演示")
    
    # 简单文本生成
    print_subsection("简单文本生成")
    try:
        result = await unified_llm_service.generate_text_analysis(
            prompt="请简要介绍股票投资的基本概念",
            system_instruction="你是一位专业的金融顾问"
        )
        
        if result:
            print(f"生成结果: {result[:200]}...")
        else:
            print("生成失败")
            
    except Exception as e:
        print(f"生成过程中发生错误: {str(e)}")
    
    # 启用搜索的文本生成
    print_subsection("启用搜索的文本生成")
    try:
        result = await unified_llm_service.generate_text_analysis(
            prompt="分析当前A股市场的整体趋势",
            system_instruction="你是专业的股票市场分析师，请基于最新信息进行分析",
            search=True
        )
        
        if result:
            print(f"基于搜索的分析: {result[:200]}...")
        else:
            print("搜索分析失败")
            
    except Exception as e:
        print(f"搜索分析过程中发生错误: {str(e)}")
    
    # 带元数据的文本生成
    print_subsection("详细响应信息")
    try:
        response = await unified_llm_service.generate_text_with_metadata(
            prompt="评估苹果公司(AAPL)的投资价值",
            system_instruction="你是资深的股票分析师",
            temperature=0.7,
            max_tokens=500
        )
        
        if response:
            print(f"内容长度: {len(response.content)} 字符")
            print(f"使用提供商: {response.provider}")
            print(f"使用模型: {response.model}")
            if response.usage:
                print(f"Token使用: {response.usage.total_tokens}")
            print(f"响应时间: {response.response_time:.2f}秒")
            print(f"内容预览: {response.content[:150]}...")
        else:
            print("详细分析失败")
            
    except Exception as e:
        print(f"详细分析过程中发生错误: {str(e)}")


async def demo_compatibility():
    """演示兼容性接口"""
    print_section("兼容性接口演示")
    
    # 使用增强Windmill客户端
    print_subsection("增强Windmill客户端")
    try:
        result = await enhanced_windmill_client.generate_text_analysis(
            prompt="分析比特币的投资风险",
            system_instruction="你是加密货币投资专家",
            search=False
        )
        
        if result:
            print(f"兼容性接口结果: {result[:200]}...")
        else:
            print("兼容性接口调用失败")
            
    except Exception as e:
        print(f"兼容性接口调用错误: {str(e)}")
    
    # 批量相关性分析
    print_subsection("批量新闻相关性分析")
    try:
        news_items = [
            {
                "title": "苹果公司发布新款iPhone",
                "content": "苹果公司今日正式发布了新款iPhone，搭载最新的A17芯片...",
                "source": "科技日报"
            },
            {
                "title": "美联储宣布加息决定",
                "content": "美联储在最新的货币政策会议上决定将利率上调25个基点...",
                "source": "财经新闻"
            }
        ]
        
        stock_info = {
            "symbol": "AAPL",
            "name": "苹果公司",
            "sector": "科技"
        }
        
        relevance_results = await enhanced_windmill_client.batch_analyze_stock_relevance(
            news_items, stock_info
        )
        
        if relevance_results:
            print(f"相关性分析结果: 共分析 {len(relevance_results)} 条新闻")
            for i, result in enumerate(relevance_results[:2]):  # 只显示前2个结果
                print(f"  新闻{i+1}: 相关性评分 {result.get('relevance_score', 'N/A')}")
        else:
            print("批量相关性分析失败")
            
    except Exception as e:
        print(f"批量分析错误: {str(e)}")


async def demo_monitoring():
    """演示监控功能"""
    print_section("监控功能演示")
    
    # 健康检查
    print_subsection("健康检查")
    try:
        health_status = await unified_llm_service.health_check()
        print("提供商健康状态:")
        for provider_id, is_healthy in health_status.items():
            status_text = "健康" if is_healthy else "异常"
            print(f"  {provider_id}: {status_text}")
            
    except Exception as e:
        print(f"健康检查错误: {str(e)}")
    
    # 服务统计
    print_subsection("服务统计信息")
    try:
        stats = unified_llm_service.get_stats()
        print(f"默认提供商: {stats.get('default_provider', 'N/A')}")
        print(f"总提供商数: {stats.get('total_providers', 0)}")
        print(f"启用提供商数: {stats.get('enabled_providers', 0)}")
        
        provider_stats = stats.get('provider_stats', {})
        if provider_stats:
            print("提供商详细统计:")
            for provider_id, provider_stat in provider_stats.items():
                print(f"  {provider_id}:")
                print(f"    请求次数: {provider_stat.get('request_count', 0)}")
                print(f"    成功次数: {provider_stat.get('success_count', 0)}")
                print(f"    成功率: {provider_stat.get('success_rate', 0):.2%}")
                
    except Exception as e:
        print(f"获取统计信息错误: {str(e)}")
    
    # 可用提供商
    print_subsection("可用提供商列表")
    try:
        providers = unified_llm_service.get_available_providers()
        print(f"当前可用提供商: {providers}")
        
    except Exception as e:
        print(f"获取提供商列表错误: {str(e)}")


async def demo_error_handling():
    """演示错误处理"""
    print_section("错误处理演示")
    
    # 参数验证错误
    print_subsection("参数验证错误")
    try:
        result = await unified_llm_service.generate_text_analysis(
            prompt="",  # 空提示词，应该触发验证错误
            system_instruction="测试验证"
        )
        print("意外成功（应该失败）")
        
    except ValidationError as e:
        print(f"捕获到验证错误: {e.message}")
    except Exception as e:
        print(f"其他错误: {str(e)}")
    
    # 通用错误处理
    print_subsection("通用错误处理示例")
    try:
        result = await unified_llm_service.generate_text_analysis(
            prompt="这是一个测试请求",
            provider_id="non_existent_provider"  # 不存在的提供商
        )
        print("意外成功（应该失败）")
        
    except LLMServiceError as e:
        print(f"LLM服务错误: {e.message}")
        print(f"错误代码: {e.error_code}")
        print(f"是否可重试: {e.is_retryable}")
    except Exception as e:
        print(f"未知错误: {str(e)}")


async def demo_streaming_responses():
    """演示流式响应功能"""
    print_section("流式响应功能演示")

    # 基础流式响应
    print_subsection("基础流式响应")
    try:
        print("开始流式文本生成...")

        chunks_received = 0
        total_content = ""

        async for chunk in unified_llm_service.generate_text_analysis_stream(
            prompt="请简要分析苹果公司的投资价值，包括技术面和基本面",
            system_instruction="你是专业的金融分析师"
        ):
            chunks_received += 1
            total_content = chunk.content

            # 显示进度信息
            progress_info = ""
            if chunk.progress_percentage:
                progress_info = f" (进度: {chunk.progress_percentage:.1f}%)"

            print(f"  块 {chunk.chunk_id}: +{len(chunk.delta)} 字符{progress_info}")

            if chunk.is_final:
                print(f"流式响应完成！总共接收 {chunks_received} 个块")
                print(f"最终内容长度: {len(total_content)} 字符")
                print(f"内容预览: {total_content[:150]}...")
                break

    except Exception as e:
        print(f"流式响应错误: {str(e)}")

    # 兼容性流式响应
    print_subsection("兼容性流式响应接口")
    try:
        print("使用增强Windmill客户端的流式接口...")

        chunks_received = 0

        async for chunk in enhanced_windmill_client.generate_text_analysis_stream(
            prompt="分析比特币的投资风险",
            system_instruction="你是加密货币投资专家"
        ):
            chunks_received += 1

            if chunk.has_error:
                print(f"  错误块: {chunk.error_message}")
                break

            print(f"  块 {chunk.chunk_id}: {chunk.provider} - {len(chunk.delta)} 字符")

            if chunk.is_final:
                print(f"兼容性流式响应完成！总共 {chunks_received} 个块")
                break

    except Exception as e:
        print(f"兼容性流式响应错误: {str(e)}")

    # 流式响应控制演示
    print_subsection("流式响应控制功能")
    try:
        print("演示流式响应取消功能...")

        # 获取活跃流式响应
        active_streams_before = unified_llm_service.get_active_streams()
        print(f"当前活跃流式响应: {len(active_streams_before)} 个")

        chunks_received = 0
        cancellation_token_id = None

        async for chunk in unified_llm_service.generate_text_analysis_stream(
            prompt="请详细分析全球经济形势对股市的影响，包括各个方面的深入分析",
            system_instruction="你是宏观经济分析专家",
            enable_cancellation=True
        ):
            chunks_received += 1

            # 获取取消令牌ID
            if chunk.cancellation_token and not cancellation_token_id:
                cancellation_token_id = chunk.cancellation_token
                print(f"  获取到取消令牌: {cancellation_token_id}")

            print(f"  块 {chunk.chunk_id}: {len(chunk.delta)} 字符")

            # 在第3个块后取消
            if chunks_received == 3 and cancellation_token_id:
                print("  执行取消操作...")
                success = unified_llm_service.cancel_stream(cancellation_token_id, "演示取消")
                print(f"  取消结果: {'成功' if success else '失败'}")

            if chunk.is_cancelled:
                print(f"  流式响应已取消: {chunk.cancellation_token}")
                break

            if chunk.is_final:
                print("  流式响应正常完成")
                break

        # 检查流式响应状态
        if cancellation_token_id:
            status = unified_llm_service.get_stream_status(cancellation_token_id)
            if status:
                print(f"  最终状态: 已取消={status['is_cancelled']}, 原因={status.get('reason', 'N/A')}")

    except Exception as e:
        print(f"流式响应控制错误: {str(e)}")


async def demo_practical_scenarios():
    """演示实际应用场景"""
    print_section("实际应用场景演示")

    # 股票分析报告（流式）
    print_subsection("股票分析报告生成（流式）")
    try:
        stock_analysis_prompt = """
        请为特斯拉(TSLA)生成一份简要的投资分析报告，包括：
        1. 公司基本面简析
        2. 近期股价表现
        3. 主要风险因素
        4. 投资建议

        请保持分析客观、专业。
        """

        print("开始生成特斯拉投资分析报告（流式）...")

        final_report = ""
        async for chunk in unified_llm_service.generate_text_analysis_stream(
            prompt=stock_analysis_prompt,
            system_instruction="你是专业的股票分析师，具有丰富的汽车行业分析经验",
            search=True,
            temperature=0.3
        ):
            if chunk.progress_percentage:
                print(f"  报告生成进度: {chunk.progress_percentage:.1f}%")

            if chunk.is_final:
                final_report = chunk.content
                print("特斯拉投资分析报告生成完成:")
                print("-" * 40)
                print(final_report[:300] + "..." if len(final_report) > 300 else final_report)
                break

    except Exception as e:
        print(f"流式报告生成错误: {str(e)}")

    # 市场情绪分析（非流式对比）
    print_subsection("市场情绪分析（非流式）")
    try:
        sentiment_prompt = """
        基于当前的市场环境，分析投资者对科技股的整体情绪，包括：
        1. 当前市场情绪倾向
        2. 影响情绪的主要因素
        3. 情绪变化对投资策略的影响

        请提供简洁的分析结论。
        """

        sentiment_analysis = await unified_llm_service.generate_text_analysis(
            prompt=sentiment_prompt,
            system_instruction="你是市场情绪分析专家，擅长解读投资者心理",
            search=True,
            temperature=0.4
        )

        if sentiment_analysis:
            print("市场情绪分析:")
            print("-" * 40)
            print(sentiment_analysis[:300] + "..." if len(sentiment_analysis) > 300 else sentiment_analysis)
        else:
            print("情绪分析失败")

    except Exception as e:
        print(f"情绪分析错误: {str(e)}")


async def main():
    """主函数"""
    print("LLM服务层功能演示")
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 依次运行各个演示
        await demo_service_status()
        await demo_basic_usage()
        await demo_compatibility()
        await demo_streaming_responses()  # 新增流式响应演示
        await demo_monitoring()
        await demo_error_handling()
        await demo_practical_scenarios()

        print_section("演示完成")
        print("所有功能演示已完成！")
        print("\n🎉 新增功能:")
        print("✅ 流式响应支持 - 实时获取LLM生成内容")
        print("✅ 智能降级机制 - 为不支持流式的提供商提供模拟流式输出")
        print("✅ 取消控制功能 - 支持中途取消流式响应")
        print("✅ 进度跟踪功能 - 实时显示生成进度")
        print("✅ 向后兼容性 - 现有代码无需修改")
        print("\n建议下一步:")
        print("1. 查看 docs/llm_service_quick_start.md 了解快速开始")
        print("2. 查看 docs/llm_service_architecture.md 了解架构设计")
        print("3. 查看 docs/llm_service_api_reference.md 了解API详情")
        print("4. 尝试使用新的流式响应接口提升用户体验")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
