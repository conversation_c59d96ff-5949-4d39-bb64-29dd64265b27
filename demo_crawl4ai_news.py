#!/usr/bin/env python3
"""
Crawl4AI 股票新闻爬取和分析演示

展示如何使用 Crawl4AI HTTP 接口爬取特定股票的最新新闻，
然后通过 Windmill 验证新闻新鲜度并分析价值。
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import StockInfo
from financial_analysis.crawl4ai_news_crawler import (
    StockNewsCrawlAnalyzer,
    Crawl4aiNewsClient,
    StockNewsCrawler,
    NewsValidator
)
from loguru import logger


def print_separator(title: str):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f" {title} ")
    print("=" * 60)


async def demo_crawl4ai_health_check():
    """演示 Crawl4AI 健康检查"""
    print_separator("Crawl4AI 健康检查")
    
    try:
        async with Crawl4aiNewsClient() as client:
            is_healthy = await client.health_check()
            
            if is_healthy:
                print("✅ Crawl4AI 服务运行正常")
                return True
            else:
                print("❌ Crawl4AI 服务不可用")
                print("请确保 Crawl4AI 服务已启动:")
                print("docker run -d -p 11235:11235 --name crawl4ai unclecode/crawl4ai:latest")
                return False
                
    except Exception as e:
        print(f"❌ 连接 Crawl4AI 服务失败: {str(e)}")
        print("请检查 Crawl4AI 服务是否在 http://localhost:11235 运行")
        return False


async def demo_basic_news_crawling():
    """演示基础新闻爬取"""
    print_separator("基础新闻爬取演示")
    
    # 测试股票
    stock_info = StockInfo(
        symbol="000001",
        name="平安银行", 
        exchange="SZSE",
        sector="金融"
    )
    
    print(f"📊 爬取股票新闻: {stock_info.name} ({stock_info.symbol})")
    
    try:
        crawler = StockNewsCrawler()
        
        # 爬取新闻（限制数量以加快演示）
        news_items = await crawler.crawl_stock_news(
            stock_info=stock_info,
            max_news_per_source=3,
            sources=["sina_finance"]  # 只使用新浪财经源
        )
        
        if news_items:
            print(f"✅ 成功爬取 {len(news_items)} 条新闻:")
            
            for i, news in enumerate(news_items[:5], 1):
                print(f"\n[{i}] {news.title}")
                print(f"    来源: {news.source}")
                print(f"    时间: {news.publish_time.strftime('%Y-%m-%d %H:%M') if news.publish_time else '未知'}")
                print(f"    链接: {news.url}")
                if news.content:
                    print(f"    摘要: {news.content[:100]}...")
        else:
            print("❌ 未爬取到新闻")
            
    except Exception as e:
        print(f"❌ 爬取失败: {str(e)}")


async def demo_news_validation():
    """演示新闻验证和分析"""
    print_separator("新闻验证和价值分析演示")
    
    stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
    
    print(f"🔍 分析股票新闻: {stock_info.name} ({stock_info.symbol})")
    
    try:
        # 先爬取新闻
        crawler = StockNewsCrawler()
        news_items = await crawler.crawl_stock_news(
            stock_info=stock_info,
            max_news_per_source=2,
            sources=["sina_finance"]
        )
        
        if not news_items:
            print("❌ 未找到新闻进行分析")
            return
        
        print(f"📰 找到 {len(news_items)} 条新闻，开始验证分析...")
        
        # 验证和分析
        validator = NewsValidator()
        analyzed_news = await validator.validate_and_analyze_news(news_items, stock_info)
        
        if analyzed_news:
            print(f"✅ 分析完成，{len(analyzed_news)} 条新鲜新闻:")
            
            for i, news in enumerate(analyzed_news, 1):
                print(f"\n[{i}] {news.title}")
                print(f"    新鲜度: {'是' if news.is_fresh else '否'} ({news.freshness_score:.0f}/100)")
                print(f"    价值评分: {news.value_score:.0f}/100")
                print(f"    影响程度: {news.impact_level}")
                print(f"    情感倾向: {news.sentiment}")
                print(f"    分析摘要: {news.analysis_summary}")
        else:
            print("❌ 未找到新鲜新闻")
            
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")


async def demo_complete_analysis():
    """演示完整的新闻分析流程"""
    print_separator("完整新闻分析流程演示")
    
    # 测试多个股票
    test_stocks = [
        StockInfo(symbol="000001", name="平安银行", exchange="SZSE", sector="金融"),
        StockInfo(symbol="000002", name="万科A", exchange="SZSE", sector="房地产")
    ]
    
    analyzer = StockNewsCrawlAnalyzer()
    
    for stock_info in test_stocks:
        print(f"\n📈 分析股票: {stock_info.name} ({stock_info.symbol})")
        
        try:
            # 获取分析后的新闻
            analyzed_news = await analyzer.get_analyzed_stock_news(
                stock_info=stock_info,
                max_news_per_source=2,
                sources=["sina_finance"],
                min_value_score=50.0  # 降低阈值以便演示
            )
            
            if analyzed_news:
                print(f"✅ 找到 {len(analyzed_news)} 条高价值新闻")
                
                # 生成报告
                report = await analyzer.generate_news_report(stock_info, analyzed_news)
                print("\n📊 新闻分析报告:")
                print(report)
            else:
                print("❌ 未找到高价值新闻")
                
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")


async def demo_custom_source_crawling():
    """演示自定义新闻源爬取"""
    print_separator("自定义新闻源爬取演示")
    
    print("🔧 演示如何扩展新闻源配置")
    
    # 显示当前支持的新闻源
    crawler = StockNewsCrawler()
    sources = list(crawler.news_sources.keys())
    
    print(f"📋 当前支持的新闻源: {', '.join(sources)}")
    
    for source_key, source_config in crawler.news_sources.items():
        print(f"\n📰 {source_config['name']}:")
        print(f"   搜索模板: {source_config['search_url_template']}")
        print(f"   提取策略: CSS选择器提取")
    
    print("\n💡 扩展建议:")
    print("1. 添加更多财经媒体网站")
    print("2. 支持不同的提取策略（XPath、LLM等）")
    print("3. 增加反爬虫措施（代理、延时等）")
    print("4. 实现增量爬取和去重机制")


async def demo_error_handling():
    """演示错误处理"""
    print_separator("错误处理演示")
    
    print("🛡️ 测试各种错误情况的处理")
    
    # 测试无效股票
    invalid_stock = StockInfo(symbol="INVALID", name="无效股票", exchange="UNKNOWN")
    
    try:
        analyzer = StockNewsCrawlAnalyzer()
        result = await analyzer.get_analyzed_stock_news(
            stock_info=invalid_stock,
            max_news_per_source=1
        )
        print(f"✅ 无效股票处理: 返回 {len(result)} 条结果")
        
    except Exception as e:
        print(f"❌ 无效股票处理失败: {str(e)}")
    
    # 测试网络错误处理
    print("\n🌐 网络错误处理:")
    print("- 连接超时: 自动重试机制")
    print("- 服务不可用: 优雅降级")
    print("- 解析失败: 默认值填充")
    print("- API限制: 批量处理和延时")


async def main():
    """主函数"""
    print("🚀 Crawl4AI 股票新闻爬取和分析演示")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 健康检查
        is_healthy = await demo_crawl4ai_health_check()
        
        if not is_healthy:
            print("\n⚠️  Crawl4AI 服务不可用，跳过实际爬取演示")
            print("以下演示将展示系统架构和配置:")
            await demo_custom_source_crawling()
            await demo_error_handling()
            return
        
        # 2. 基础爬取演示
        await demo_basic_news_crawling()
        
        # 3. 新闻验证演示
        await demo_news_validation()
        
        # 4. 完整分析流程
        await demo_complete_analysis()
        
        # 5. 自定义源演示
        await demo_custom_source_crawling()
        
        # 6. 错误处理演示
        await demo_error_handling()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {str(e)}")
        logger.exception("演示程序异常")
    
    print(f"\n✅ 演示完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n📋 总结:")
    print("1. ✅ Crawl4AI HTTP 接口集成")
    print("2. ✅ 多源新闻爬取策略")
    print("3. ✅ Windmill 新闻验证分析")
    print("4. ✅ 智能价值评估算法")
    print("5. ✅ 完整的错误处理机制")
    print("6. ✅ 可扩展的架构设计")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr, 
        level="INFO", 
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 运行演示
    asyncio.run(main())
